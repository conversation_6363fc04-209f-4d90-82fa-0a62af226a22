' نظام نيو جرافيك المحاسبي المتكامل
' شركة نيو جرافيك للدعاية والإعلان
' تطوير: نظام محاسبي شامل بـ VBA

Option Explicit

' متغيرات عامة
Public CurrentUser As String
Public UserPermissions As String
Public CompanyName As String
Public CompanyCurrency As String

' إعدادات الشركة
Sub InitializeSystem()
    CompanyName = "شركة نيو جرافيك للدعاية والإعلان"
    CompanyCurrency = "جنيه مصري"
    
    ' إنشاء أوراق العمل الأساسية
    Call CreateWorksheets
    Call SetupDashboard
    Call SetupUserSystem
End Sub

' إنشاء أوراق العمل
Sub CreateWorksheets()
    Dim ws As Worksheet
    Dim wsNames As Variant
    Dim i As Integer
    
    wsNames = Array("لوحة_التحكم", "العملاء", "الموردين", "الأصناف", _
                   "فواتير_المبيعات", "فواتير_المشتريات", "المصروفات", _
                   "الإيرادات", "التقارير", "كشف_الحسابات", "المستخدمين", "الإعدادات")
    
    ' حذف الأوراق الموجودة عدا الأولى
    Application.DisplayAlerts = False
    For Each ws In ThisWorkbook.Worksheets
        If ws.Index > 1 Then ws.Delete
    Next ws
    Application.DisplayAlerts = True
    
    ' إنشاء الأوراق الجديدة
    For i = 0 To UBound(wsNames)
        If i = 0 Then
            ThisWorkbook.Worksheets(1).Name = wsNames(i)
        Else
            Set ws = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(ThisWorkbook.Worksheets.Count))
            ws.Name = wsNames(i)
        End If
    Next i
End Sub

' إعداد لوحة التحكم
Sub SetupDashboard()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("لوحة_التحكم")
    
    With ws
        .Cells.Clear
        
        ' عنوان الشركة
        .Range("A1:J1").Merge
        .Range("A1").Value = CompanyName
        .Range("A1").Font.Size = 18
        .Range("A1").Font.Bold = True
        .Range("A1").HorizontalAlignment = xlCenter
        .Range("A1").Interior.Color = RGB(0, 112, 192)
        .Range("A1").Font.Color = RGB(255, 255, 255)
        
        ' التاريخ والوقت
        .Range("A2").Value = "التاريخ: " & Format(Date, "dd/mm/yyyy")
        .Range("F2").Value = "الوقت: " & Format(Time, "hh:mm AM/PM")
        
        ' أزرار التنقل الرئيسية
        Call CreateNavigationButtons(ws)
        
        ' ملخص مالي سريع
        Call CreateFinancialSummary(ws)
        
        ' جدول آخر العمليات
        Call CreateRecentTransactions(ws)
    End With
End Sub

' إنشاء أزرار التنقل
Sub CreateNavigationButtons(ws As Worksheet)
    Dim btnTop As Integer, btnLeft As Integer
    Dim btnWidth As Integer, btnHeight As Integer
    Dim i As Integer, j As Integer
    
    btnTop = 80
    btnLeft = 50
    btnWidth = 120
    btnHeight = 40
    
    Dim buttons As Variant
    buttons = Array("إدارة العملاء", "إدارة الموردين", "إدارة الأصناف", _
                   "فواتير المبيعات", "فواتير المشتريات", "المصروفات والإيرادات", _
                   "التقارير المالية", "كشف الحسابات", "إدارة المستخدمين")
    
    For i = 0 To UBound(buttons)
        j = i Mod 3
        If i > 0 And j = 0 Then btnTop = btnTop + 60
        
        With ws.Shapes.AddShape(msoShapeRoundedRectangle, _
                               btnLeft + (j * 150), btnTop, btnWidth, btnHeight)
            .TextFrame.Characters.Text = buttons(i)
            .TextFrame.Characters.Font.Size = 10
            .TextFrame.Characters.Font.Bold = True
            .Fill.ForeColor.RGB = RGB(68, 114, 196)
            .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
            .OnAction = "Navigate_" & Replace(buttons(i), " ", "_")
        End With
    Next i
End Sub

' ملخص مالي
Sub CreateFinancialSummary(ws As Worksheet)
    With ws
        .Range("A15").Value = "الملخص المالي السريع"
        .Range("A15").Font.Size = 14
        .Range("A15").Font.Bold = True
        
        .Range("A17").Value = "إجمالي المبيعات (الشهر الحالي):"
        .Range("C17").Value = "=SUMIF(فواتير_المبيعات.C:C,MONTH(TODAY()),فواتير_المبيعات.F:F)"
        .Range("C17").NumberFormat = "#,##0.00 ""ج.م"""
        
        .Range("A18").Value = "إجمالي المشتريات (الشهر الحالي):"
        .Range("C18").Value = "=SUMIF(فواتير_المشتريات.C:C,MONTH(TODAY()),فواتير_المشتريات.F:F)"
        .Range("C18").NumberFormat = "#,##0.00 ""ج.م"""
        
        .Range("A19").Value = "صافي الربح (الشهر الحالي):"
        .Range("C19").Value = "=C17-C18"
        .Range("C19").NumberFormat = "#,##0.00 ""ج.م"""
        .Range("C19").Font.Bold = True
        
        .Range("A20").Value = "عدد العملاء النشطين:"
        .Range("C20").Value = "=COUNTA(العملاء.A:A)-1"
        
        .Range("A21").Value = "عدد الموردين النشطين:"
        .Range("C21").Value = "=COUNTA(الموردين.A:A)-1"
    End With
End Sub

' آخر العمليات
Sub CreateRecentTransactions(ws As Worksheet)
    With ws
        .Range("F15").Value = "آخر العمليات"
        .Range("F15").Font.Size = 14
        .Range("F15").Font.Bold = True
        
        ' عناوين الجدول
        .Range("F17").Value = "التاريخ"
        .Range("G17").Value = "النوع"
        .Range("H17").Value = "العميل/المورد"
        .Range("I17").Value = "المبلغ"
        .Range("F17:I17").Font.Bold = True
        .Range("F17:I17").Interior.Color = RGB(217, 217, 217)
    End With
End Sub

' نظام المستخدمين والصلاحيات
Sub SetupUserSystem()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المستخدمين")
    
    With ws
        .Cells.Clear
        .Range("A1").Value = "اسم المستخدم"
        .Range("B1").Value = "كلمة المرور"
        .Range("C1").Value = "نوع الصلاحية"
        .Range("D1").Value = "تاريخ الإنشاء"
        .Range("E1").Value = "آخر دخول"
        .Range("F1").Value = "حالة النشاط"
        
        ' مستخدم افتراضي
        .Range("A2").Value = "admin"
        .Range("B2").Value = "123456"
        .Range("C2").Value = "مدير عام"
        .Range("D2").Value = Date
        .Range("F2").Value = "نشط"
        
        .Range("A1:F1").Font.Bold = True
        .Range("A1:F1").Interior.Color = RGB(68, 114, 196)
        .Range("A1:F1").Font.Color = RGB(255, 255, 255)
    End With
End Sub

' دالة تسجيل الدخول
Function UserLogin() As Boolean
    Dim username As String, password As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    
    Set ws = ThisWorkbook.Worksheets("المستخدمين")
    
    username = InputBox("اسم المستخدم:", "تسجيل الدخول")
    If username = "" Then Exit Function
    
    password = InputBox("كلمة المرور:", "تسجيل الدخول")
    If password = "" Then Exit Function
    
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = username And ws.Cells(i, 2).Value = password Then
            If ws.Cells(i, 6).Value = "نشط" Then
                CurrentUser = username
                UserPermissions = ws.Cells(i, 3).Value
                ws.Cells(i, 5).Value = Now
                UserLogin = True
                MsgBox "مرحباً " & username & vbCrLf & "تم تسجيل الدخول بنجاح", vbInformation
                Exit Function
            Else
                MsgBox "هذا المستخدم غير نشط", vbExclamation
                Exit Function
            End If
        End If
    Next i
    
    MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbCritical
    UserLogin = False
End Function

' ==== وحدة إدارة العملاء ====
Sub Navigate_إدارة_العملاء()
    Call SetupCustomersSheet
    ThisWorkbook.Worksheets("العملاء").Activate
End Sub

Sub SetupCustomersSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("العملاء")

    With ws
        .Cells.Clear
        .Range("A1").Value = "كود العميل"
        .Range("B1").Value = "اسم العميل"
        .Range("C1").Value = "العنوان"
        .Range("D1").Value = "التليفون"
        .Range("E1").Value = "الموبايل"
        .Range("F1").Value = "البريد الإلكتروني"
        .Range("G1").Value = "الرقم الضريبي"
        .Range("H1").Value = "حد الائتمان"
        .Range("I1").Value = "الرصيد الحالي"
        .Range("J1").Value = "تاريخ التسجيل"
        .Range("K1").Value = "ملاحظات"

        .Range("A1:K1").Font.Bold = True
        .Range("A1:K1").Interior.Color = RGB(68, 114, 196)
        .Range("A1:K1").Font.Color = RGB(255, 255, 255)
        .Columns("A:K").AutoFit

        ' إضافة أزرار العمليات
        Call AddCustomerButtons(ws)
    End With
End Sub

Sub AddCustomerButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر إضافة عميل جديد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 100, 30)
    With btn
        .TextFrame.Characters.Text = "عميل جديد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "AddNewCustomer"
    End With

    ' زر تعديل عميل
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 160, 30, 100, 30)
    With btn
        .TextFrame.Characters.Text = "تعديل عميل"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 192, 0)
        .TextFrame.Characters.Font.Color = RGB(0, 0, 0)
        .OnAction = "EditCustomer"
    End With

    ' زر حذف عميل
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 270, 30, 100, 30)
    With btn
        .TextFrame.Characters.Text = "حذف عميل"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 0, 0)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "DeleteCustomer"
    End With
End Sub

' إضافة عميل جديد
Sub AddNewCustomer()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim customerCode As String

    Set ws = ThisWorkbook.Worksheets("العملاء")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء كود عميل تلقائي
    customerCode = "C" & Format(lastRow - 1, "0000")

    ' نموذج إدخال بيانات العميل
    Dim customerName As String, address As String, phone As String
    Dim mobile As String, email As String, taxNumber As String
    Dim creditLimit As Double, notes As String

    customerName = InputBox("اسم العميل:", "عميل جديد")
    If customerName = "" Then Exit Sub

    address = InputBox("العنوان:", "عميل جديد")
    phone = InputBox("التليفون:", "عميل جديد")
    mobile = InputBox("الموبايل:", "عميل جديد")
    email = InputBox("البريد الإلكتروني:", "عميل جديد")
    taxNumber = InputBox("الرقم الضريبي:", "عميل جديد")
    creditLimit = Val(InputBox("حد الائتمان:", "عميل جديد", "0"))
    notes = InputBox("ملاحظات:", "عميل جديد")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = customerCode
        .Cells(lastRow, 2).Value = customerName
        .Cells(lastRow, 3).Value = address
        .Cells(lastRow, 4).Value = phone
        .Cells(lastRow, 5).Value = mobile
        .Cells(lastRow, 6).Value = email
        .Cells(lastRow, 7).Value = taxNumber
        .Cells(lastRow, 8).Value = creditLimit
        .Cells(lastRow, 9).Value = 0 ' الرصيد الحالي
        .Cells(lastRow, 10).Value = Date
        .Cells(lastRow, 11).Value = notes
    End With

    MsgBox "تم إضافة العميل بنجاح" & vbCrLf & "كود العميل: " & customerCode, vbInformation
End Sub

' ==== وحدة إدارة الموردين ====
Sub Navigate_إدارة_الموردين()
    Call SetupSuppliersSheet
    ThisWorkbook.Worksheets("الموردين").Activate
End Sub

Sub SetupSuppliersSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("الموردين")

    With ws
        .Cells.Clear
        .Range("A1").Value = "كود المورد"
        .Range("B1").Value = "اسم المورد"
        .Range("C1").Value = "العنوان"
        .Range("D1").Value = "التليفون"
        .Range("E1").Value = "الموبايل"
        .Range("F1").Value = "البريد الإلكتروني"
        .Range("G1").Value = "الرقم الضريبي"
        .Range("H1").Value = "شروط الدفع"
        .Range("I1").Value = "الرصيد الحالي"
        .Range("J1").Value = "تاريخ التسجيل"
        .Range("K1").Value = "ملاحظات"

        .Range("A1:K1").Font.Bold = True
        .Range("A1:K1").Interior.Color = RGB(68, 114, 196)
        .Range("A1:K1").Font.Color = RGB(255, 255, 255)
        .Columns("A:K").AutoFit

        ' إضافة أزرار العمليات
        Call AddSupplierButtons(ws)
    End With
End Sub

Sub AddSupplierButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر إضافة مورد جديد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 100, 30)
    With btn
        .TextFrame.Characters.Text = "مورد جديد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "AddNewSupplier"
    End With
End Sub

' إضافة مورد جديد
Sub AddNewSupplier()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim supplierCode As String

    Set ws = ThisWorkbook.Worksheets("الموردين")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء كود مورد تلقائي
    supplierCode = "S" & Format(lastRow - 1, "0000")

    ' نموذج إدخال بيانات المورد
    Dim supplierName As String, address As String, phone As String
    Dim mobile As String, email As String, taxNumber As String
    Dim paymentTerms As String, notes As String

    supplierName = InputBox("اسم المورد:", "مورد جديد")
    If supplierName = "" Then Exit Sub

    address = InputBox("العنوان:", "مورد جديد")
    phone = InputBox("التليفون:", "مورد جديد")
    mobile = InputBox("الموبايل:", "مورد جديد")
    email = InputBox("البريد الإلكتروني:", "مورد جديد")
    taxNumber = InputBox("الرقم الضريبي:", "مورد جديد")
    paymentTerms = InputBox("شروط الدفع:", "مورد جديد", "نقدي")
    notes = InputBox("ملاحظات:", "مورد جديد")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = supplierCode
        .Cells(lastRow, 2).Value = supplierName
        .Cells(lastRow, 3).Value = address
        .Cells(lastRow, 4).Value = phone
        .Cells(lastRow, 5).Value = mobile
        .Cells(lastRow, 6).Value = email
        .Cells(lastRow, 7).Value = taxNumber
        .Cells(lastRow, 8).Value = paymentTerms
        .Cells(lastRow, 9).Value = 0 ' الرصيد الحالي
        .Cells(lastRow, 10).Value = Date
        .Cells(lastRow, 11).Value = notes
    End With

    MsgBox "تم إضافة المورد بنجاح" & vbCrLf & "كود المورد: " & supplierCode, vbInformation
End Sub

' ==== وحدة إدارة الأصناف ====
Sub Navigate_إدارة_الأصناف()
    Call SetupItemsSheet
    ThisWorkbook.Worksheets("الأصناف").Activate
End Sub

Sub SetupItemsSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("الأصناف")

    With ws
        .Cells.Clear
        .Range("A1").Value = "كود الصنف"
        .Range("B1").Value = "اسم الصنف"
        .Range("C1").Value = "الوصف"
        .Range("D1").Value = "الوحدة"
        .Range("E1").Value = "سعر الشراء"
        .Range("F1").Value = "سعر البيع"
        .Range("G1").Value = "الكمية المتاحة"
        .Range("H1").Value = "الحد الأدنى"
        .Range("I1").Value = "المجموعة"
        .Range("J1").Value = "تاريخ الإضافة"
        .Range("K1").Value = "ملاحظات"

        .Range("A1:K1").Font.Bold = True
        .Range("A1:K1").Interior.Color = RGB(68, 114, 196)
        .Range("A1:K1").Font.Color = RGB(255, 255, 255)
        .Columns("A:K").AutoFit

        ' إضافة أزرار العمليات
        Call AddItemButtons(ws)
    End With
End Sub

Sub AddItemButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر إضافة صنف جديد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 100, 30)
    With btn
        .TextFrame.Characters.Text = "صنف جديد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "AddNewItem"
    End With
End Sub

' إضافة صنف جديد
Sub AddNewItem()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim itemCode As String

    Set ws = ThisWorkbook.Worksheets("الأصناف")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء كود صنف تلقائي
    itemCode = "I" & Format(lastRow - 1, "0000")

    ' نموذج إدخال بيانات الصنف
    Dim itemName As String, description As String, unit As String
    Dim purchasePrice As Double, salePrice As Double
    Dim quantity As Double, minQuantity As Double
    Dim category As String, notes As String

    itemName = InputBox("اسم الصنف:", "صنف جديد")
    If itemName = "" Then Exit Sub

    description = InputBox("الوصف:", "صنف جديد")
    unit = InputBox("الوحدة:", "صنف جديد", "قطعة")
    purchasePrice = Val(InputBox("سعر الشراء:", "صنف جديد", "0"))
    salePrice = Val(InputBox("سعر البيع:", "صنف جديد", "0"))
    quantity = Val(InputBox("الكمية المتاحة:", "صنف جديد", "0"))
    minQuantity = Val(InputBox("الحد الأدنى:", "صنف جديد", "0"))
    category = InputBox("المجموعة:", "صنف جديد", "مطبوعات")
    notes = InputBox("ملاحظات:", "صنف جديد")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = itemCode
        .Cells(lastRow, 2).Value = itemName
        .Cells(lastRow, 3).Value = description
        .Cells(lastRow, 4).Value = unit
        .Cells(lastRow, 5).Value = purchasePrice
        .Cells(lastRow, 6).Value = salePrice
        .Cells(lastRow, 7).Value = quantity
        .Cells(lastRow, 8).Value = minQuantity
        .Cells(lastRow, 9).Value = category
        .Cells(lastRow, 10).Value = Date
        .Cells(lastRow, 11).Value = notes
    End With

    MsgBox "تم إضافة الصنف بنجاح" & vbCrLf & "كود الصنف: " & itemCode, vbInformation
End Sub

' ==== وحدة فواتير المبيعات ====
Sub Navigate_فواتير_المبيعات()
    Call SetupSalesInvoicesSheet
    ThisWorkbook.Worksheets("فواتير_المبيعات").Activate
End Sub

Sub SetupSalesInvoicesSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("فواتير_المبيعات")

    With ws
        .Cells.Clear
        .Range("A1").Value = "رقم الفاتورة"
        .Range("B1").Value = "التاريخ"
        .Range("C1").Value = "كود العميل"
        .Range("D1").Value = "اسم العميل"
        .Range("E1").Value = "إجمالي قبل الضريبة"
        .Range("F1").Value = "الضريبة"
        .Range("G1").Value = "الإجمالي النهائي"
        .Range("H1").Value = "المدفوع"
        .Range("I1").Value = "المتبقي"
        .Range("J1").Value = "طريقة الدفع"
        .Range("K1").Value = "الحالة"
        .Range("L1").Value = "ملاحظات"

        .Range("A1:L1").Font.Bold = True
        .Range("A1:L1").Interior.Color = RGB(68, 114, 196)
        .Range("A1:L1").Font.Color = RGB(255, 255, 255)
        .Columns("A:L").AutoFit

        ' إضافة أزرار العمليات
        Call AddSalesInvoiceButtons(ws)
    End With
End Sub

Sub AddSalesInvoiceButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر فاتورة جديدة
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 120, 30)
    With btn
        .TextFrame.Characters.Text = "فاتورة جديدة"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "CreateNewSalesInvoice"
    End With

    ' زر طباعة فاتورة
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 180, 30, 120, 30)
    With btn
        .TextFrame.Characters.Text = "طباعة فاتورة"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(68, 114, 196)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "PrintSalesInvoice"
    End With
End Sub

' إنشاء فاتورة مبيعات جديدة
Sub CreateNewSalesInvoice()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim invoiceNumber As String

    Set ws = ThisWorkbook.Worksheets("فواتير_المبيعات")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء رقم فاتورة تلقائي
    invoiceNumber = "INV" & Format(Date, "yyyymmdd") & Format(lastRow - 1, "000")

    ' اختيار العميل
    Dim customerCode As String, customerName As String
    customerCode = InputBox("كود العميل:", "فاتورة مبيعات جديدة")
    If customerCode = "" Then Exit Sub

    ' البحث عن اسم العميل
    customerName = GetCustomerName(customerCode)
    If customerName = "" Then
        MsgBox "العميل غير موجود", vbExclamation
        Exit Sub
    End If

    ' إدخال بيانات الفاتورة الأساسية
    Dim subtotal As Double, tax As Double, total As Double
    Dim paid As Double, remaining As Double
    Dim paymentMethod As String, notes As String

    subtotal = Val(InputBox("الإجمالي قبل الضريبة:", "فاتورة مبيعات", "0"))
    tax = subtotal * 0.14 ' ضريبة القيمة المضافة 14%
    total = subtotal + tax
    paid = Val(InputBox("المبلغ المدفوع:", "فاتورة مبيعات", total))
    remaining = total - paid
    paymentMethod = InputBox("طريقة الدفع:", "فاتورة مبيعات", "نقدي")
    notes = InputBox("ملاحظات:", "فاتورة مبيعات")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = invoiceNumber
        .Cells(lastRow, 2).Value = Date
        .Cells(lastRow, 3).Value = customerCode
        .Cells(lastRow, 4).Value = customerName
        .Cells(lastRow, 5).Value = subtotal
        .Cells(lastRow, 6).Value = tax
        .Cells(lastRow, 7).Value = total
        .Cells(lastRow, 8).Value = paid
        .Cells(lastRow, 9).Value = remaining
        .Cells(lastRow, 10).Value = paymentMethod
        .Cells(lastRow, 11).Value = IIf(remaining = 0, "مدفوعة", "جزئية")
        .Cells(lastRow, 12).Value = notes
    End With

    ' تحديث رصيد العميل
    Call UpdateCustomerBalance(customerCode, remaining)

    MsgBox "تم إنشاء الفاتورة بنجاح" & vbCrLf & "رقم الفاتورة: " & invoiceNumber, vbInformation
End Sub

' ==== الدوال المساعدة ====
Function GetCustomerName(customerCode As String) As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long

    Set ws = ThisWorkbook.Worksheets("العملاء")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = customerCode Then
            GetCustomerName = ws.Cells(i, 2).Value
            Exit Function
        End If
    Next i

    GetCustomerName = ""
End Function

Function GetSupplierName(supplierCode As String) As String
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long

    Set ws = ThisWorkbook.Worksheets("الموردين")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = supplierCode Then
            GetSupplierName = ws.Cells(i, 2).Value
            Exit Function
        End If
    Next i

    GetSupplierName = ""
End Function

Sub UpdateCustomerBalance(customerCode As String, amount As Double)
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long

    Set ws = ThisWorkbook.Worksheets("العملاء")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = customerCode Then
            ws.Cells(i, 9).Value = ws.Cells(i, 9).Value + amount
            Exit Sub
        End If
    Next i
End Sub

Sub UpdateSupplierBalance(supplierCode As String, amount As Double)
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long

    Set ws = ThisWorkbook.Worksheets("الموردين")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = supplierCode Then
            ws.Cells(i, 9).Value = ws.Cells(i, 9).Value + amount
            Exit Sub
        End If
    Next i
End Sub

' ==== وحدة التقارير ====
Sub Navigate_التقارير_المالية()
    Call SetupReportsSheet
    ThisWorkbook.Worksheets("التقارير").Activate
End Sub

Sub SetupReportsSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("التقارير")

    With ws
        .Cells.Clear
        .Range("A1").Value = "التقارير المالية - شركة نيو جرافيك للدعاية والإعلان"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter

        ' أزرار التقارير
        Call AddReportButtons(ws)
    End With
End Sub

Sub AddReportButtons(ws As Worksheet)
    Dim btn As Shape
    Dim btnTop As Integer

    btnTop = 80

    ' تقرير المبيعات
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "تقرير المبيعات"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateSalesReport"
    End With

    ' تقرير المشتريات
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 220, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "تقرير المشتريات"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 192, 0)
        .TextFrame.Characters.Font.Color = RGB(0, 0, 0)
        .OnAction = "GeneratePurchaseReport"
    End With

    btnTop = btnTop + 60

    ' تقرير الأرباح والخسائر
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "الأرباح والخسائر"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(68, 114, 196)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateProfitLossReport"
    End With

    ' تقرير العملاء
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 220, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "تقرير العملاء"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 0, 0)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateCustomerReport"
    End With
End Sub

' تقرير المبيعات
Sub GenerateSalesReport()
    Dim ws As Worksheet
    Dim reportWs As Worksheet
    Dim lastRow As Long, i As Long
    Dim totalSales As Double
    Dim fromDate As Date, toDate As Date

    ' إدخال فترة التقرير
    fromDate = CDate(InputBox("من تاريخ (dd/mm/yyyy):", "تقرير المبيعات", Format(Date - 30, "dd/mm/yyyy")))
    toDate = CDate(InputBox("إلى تاريخ (dd/mm/yyyy):", "تقرير المبيعات", Format(Date, "dd/mm/yyyy")))

    Set ws = ThisWorkbook.Worksheets("فواتير_المبيعات")
    Set reportWs = ThisWorkbook.Worksheets("التقارير")

    ' مسح التقرير السابق
    reportWs.Range("A10:Z100").Clear

    ' عنوان التقرير
    reportWs.Range("A10").Value = "تقرير المبيعات من " & Format(fromDate, "dd/mm/yyyy") & " إلى " & Format(toDate, "dd/mm/yyyy")
    reportWs.Range("A10").Font.Bold = True
    reportWs.Range("A10").Font.Size = 14

    ' عناوين الأعمدة
    reportWs.Range("A12").Value = "رقم الفاتورة"
    reportWs.Range("B12").Value = "التاريخ"
    reportWs.Range("C12").Value = "العميل"
    reportWs.Range("D12").Value = "الإجمالي"
    reportWs.Range("E12").Value = "المدفوع"
    reportWs.Range("F12").Value = "المتبقي"
    reportWs.Range("A12:F12").Font.Bold = True

    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    Dim reportRow As Long
    reportRow = 13

    For i = 2 To lastRow
        If ws.Cells(i, 2).Value >= fromDate And ws.Cells(i, 2).Value <= toDate Then
            reportWs.Cells(reportRow, 1).Value = ws.Cells(i, 1).Value
            reportWs.Cells(reportRow, 2).Value = ws.Cells(i, 2).Value
            reportWs.Cells(reportRow, 3).Value = ws.Cells(i, 4).Value
            reportWs.Cells(reportRow, 4).Value = ws.Cells(i, 7).Value
            reportWs.Cells(reportRow, 5).Value = ws.Cells(i, 8).Value
            reportWs.Cells(reportRow, 6).Value = ws.Cells(i, 9).Value

            totalSales = totalSales + ws.Cells(i, 7).Value
            reportRow = reportRow + 1
        End If
    Next i

    ' إجمالي المبيعات
    reportWs.Cells(reportRow + 1, 3).Value = "إجمالي المبيعات:"
    reportWs.Cells(reportRow + 1, 4).Value = totalSales
    reportWs.Cells(reportRow + 1, 4).Font.Bold = True
    reportWs.Cells(reportRow + 1, 4).NumberFormat = "#,##0.00 ""ج.م"""

    MsgBox "تم إنشاء تقرير المبيعات بنجاح", vbInformation
End Sub

' ==== وحدة فواتير المشتريات ====
Sub Navigate_فواتير_المشتريات()
    Call SetupPurchaseInvoicesSheet
    ThisWorkbook.Worksheets("فواتير_المشتريات").Activate
End Sub

Sub SetupPurchaseInvoicesSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("فواتير_المشتريات")

    With ws
        .Cells.Clear
        .Range("A1").Value = "رقم الفاتورة"
        .Range("B1").Value = "التاريخ"
        .Range("C1").Value = "كود المورد"
        .Range("D1").Value = "اسم المورد"
        .Range("E1").Value = "إجمالي قبل الضريبة"
        .Range("F1").Value = "الضريبة"
        .Range("G1").Value = "الإجمالي النهائي"
        .Range("H1").Value = "المدفوع"
        .Range("I1").Value = "المتبقي"
        .Range("J1").Value = "طريقة الدفع"
        .Range("K1").Value = "الحالة"
        .Range("L1").Value = "ملاحظات"

        .Range("A1:L1").Font.Bold = True
        .Range("A1:L1").Interior.Color = RGB(255, 192, 0)
        .Range("A1:L1").Font.Color = RGB(0, 0, 0)
        .Columns("A:L").AutoFit

        ' إضافة أزرار العمليات
        Call AddPurchaseInvoiceButtons(ws)
    End With
End Sub

Sub AddPurchaseInvoiceButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر فاتورة مشتريات جديدة
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 120, 30)
    With btn
        .TextFrame.Characters.Text = "فاتورة جديدة"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 192, 0)
        .TextFrame.Characters.Font.Color = RGB(0, 0, 0)
        .OnAction = "CreateNewPurchaseInvoice"
    End With
End Sub

' إنشاء فاتورة مشتريات جديدة
Sub CreateNewPurchaseInvoice()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim invoiceNumber As String

    Set ws = ThisWorkbook.Worksheets("فواتير_المشتريات")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء رقم فاتورة تلقائي
    invoiceNumber = "PUR" & Format(Date, "yyyymmdd") & Format(lastRow - 1, "000")

    ' اختيار المورد
    Dim supplierCode As String, supplierName As String
    supplierCode = InputBox("كود المورد:", "فاتورة مشتريات جديدة")
    If supplierCode = "" Then Exit Sub

    ' البحث عن اسم المورد
    supplierName = GetSupplierName(supplierCode)
    If supplierName = "" Then
        MsgBox "المورد غير موجود", vbExclamation
        Exit Sub
    End If

    ' إدخال بيانات الفاتورة
    Dim subtotal As Double, tax As Double, total As Double
    Dim paid As Double, remaining As Double
    Dim paymentMethod As String, notes As String

    subtotal = Val(InputBox("الإجمالي قبل الضريبة:", "فاتورة مشتريات", "0"))
    tax = subtotal * 0.14 ' ضريبة القيمة المضافة 14%
    total = subtotal + tax
    paid = Val(InputBox("المبلغ المدفوع:", "فاتورة مشتريات", total))
    remaining = total - paid
    paymentMethod = InputBox("طريقة الدفع:", "فاتورة مشتريات", "نقدي")
    notes = InputBox("ملاحظات:", "فاتورة مشتريات")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = invoiceNumber
        .Cells(lastRow, 2).Value = Date
        .Cells(lastRow, 3).Value = supplierCode
        .Cells(lastRow, 4).Value = supplierName
        .Cells(lastRow, 5).Value = subtotal
        .Cells(lastRow, 6).Value = tax
        .Cells(lastRow, 7).Value = total
        .Cells(lastRow, 8).Value = paid
        .Cells(lastRow, 9).Value = remaining
        .Cells(lastRow, 10).Value = paymentMethod
        .Cells(lastRow, 11).Value = IIf(remaining = 0, "مدفوعة", "جزئية")
        .Cells(lastRow, 12).Value = notes
    End With

    ' تحديث رصيد المورد
    Call UpdateSupplierBalance(supplierCode, -remaining)

    MsgBox "تم إنشاء فاتورة المشتريات بنجاح" & vbCrLf & "رقم الفاتورة: " & invoiceNumber, vbInformation
End Sub

' ==== وحدة المصروفات والإيرادات ====
Sub Navigate_المصروفات_والإيرادات()
    Call SetupExpensesSheet
    ThisWorkbook.Worksheets("المصروفات").Activate
End Sub

Sub SetupExpensesSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("المصروفات")

    With ws
        .Cells.Clear
        .Range("A1").Value = "رقم المصروف"
        .Range("B1").Value = "التاريخ"
        .Range("C1").Value = "نوع المصروف"
        .Range("D1").Value = "المبلغ"
        .Range("E1").Value = "الوصف"
        .Range("F1").Value = "طريقة الدفع"
        .Range("G1").Value = "المستخدم"
        .Range("H1").Value = "ملاحظات"

        .Range("A1:H1").Font.Bold = True
        .Range("A1:H1").Interior.Color = RGB(255, 0, 0)
        .Range("A1:H1").Font.Color = RGB(255, 255, 255)
        .Columns("A:H").AutoFit

        ' إضافة أزرار العمليات
        Call AddExpenseButtons(ws)
    End With
End Sub

Sub AddExpenseButtons(ws As Worksheet)
    Dim btn As Shape

    ' زر مصروف جديد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, 30, 120, 30)
    With btn
        .TextFrame.Characters.Text = "مصروف جديد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 0, 0)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "AddNewExpense"
    End With

    ' زر إيراد جديد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 180, 30, 120, 30)
    With btn
        .TextFrame.Characters.Text = "إيراد جديد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "AddNewRevenue"
    End With
End Sub

' إضافة مصروف جديد
Sub AddNewExpense()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim expenseNumber As String

    Set ws = ThisWorkbook.Worksheets("المصروفات")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء رقم مصروف تلقائي
    expenseNumber = "EXP" & Format(Date, "yyyymmdd") & Format(lastRow - 1, "000")

    ' إدخال بيانات المصروف
    Dim expenseType As String, amount As Double, description As String
    Dim paymentMethod As String, notes As String

    expenseType = InputBox("نوع المصروف:", "مصروف جديد", "مصروفات تشغيلية")
    If expenseType = "" Then Exit Sub

    amount = Val(InputBox("المبلغ:", "مصروف جديد", "0"))
    If amount <= 0 Then
        MsgBox "يجب إدخال مبلغ صحيح", vbExclamation
        Exit Sub
    End If

    description = InputBox("الوصف:", "مصروف جديد")
    paymentMethod = InputBox("طريقة الدفع:", "مصروف جديد", "نقدي")
    notes = InputBox("ملاحظات:", "مصروف جديد")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = expenseNumber
        .Cells(lastRow, 2).Value = Date
        .Cells(lastRow, 3).Value = expenseType
        .Cells(lastRow, 4).Value = amount
        .Cells(lastRow, 5).Value = description
        .Cells(lastRow, 6).Value = paymentMethod
        .Cells(lastRow, 7).Value = CurrentUser
        .Cells(lastRow, 8).Value = notes
    End With

    MsgBox "تم إضافة المصروف بنجاح" & vbCrLf & "رقم المصروف: " & expenseNumber, vbInformation
End Sub

' إضافة إيراد جديد
Sub AddNewRevenue()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim revenueNumber As String

    Set ws = ThisWorkbook.Worksheets("الإيرادات")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1

    ' إنشاء رقم إيراد تلقائي
    revenueNumber = "REV" & Format(Date, "yyyymmdd") & Format(lastRow - 1, "000")

    ' إدخال بيانات الإيراد
    Dim revenueType As String, amount As Double, description As String
    Dim paymentMethod As String, notes As String

    revenueType = InputBox("نوع الإيراد:", "إيراد جديد", "إيرادات أخرى")
    If revenueType = "" Then Exit Sub

    amount = Val(InputBox("المبلغ:", "إيراد جديد", "0"))
    If amount <= 0 Then
        MsgBox "يجب إدخال مبلغ صحيح", vbExclamation
        Exit Sub
    End If

    description = InputBox("الوصف:", "إيراد جديد")
    paymentMethod = InputBox("طريقة الاستلام:", "إيراد جديد", "نقدي")
    notes = InputBox("ملاحظات:", "إيراد جديد")

    ' إدخال البيانات
    With ws
        .Cells(lastRow, 1).Value = revenueNumber
        .Cells(lastRow, 2).Value = Date
        .Cells(lastRow, 3).Value = revenueType
        .Cells(lastRow, 4).Value = amount
        .Cells(lastRow, 5).Value = description
        .Cells(lastRow, 6).Value = paymentMethod
        .Cells(lastRow, 7).Value = CurrentUser
        .Cells(lastRow, 8).Value = notes
    End With

    MsgBox "تم إضافة الإيراد بنجاح" & vbCrLf & "رقم الإيراد: " & revenueNumber, vbInformation
End Sub

' ==== وحدة كشف الحسابات ====
Sub Navigate_كشف_الحسابات()
    Call SetupAccountStatementsSheet
    ThisWorkbook.Worksheets("كشف_الحسابات").Activate
End Sub

Sub SetupAccountStatementsSheet()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("كشف_الحسابات")

    With ws
        .Cells.Clear
        .Range("A1").Value = "كشوف الحسابات - شركة نيو جرافيك للدعاية والإعلان"
        .Range("A1").Font.Size = 16
        .Range("A1").Font.Bold = True
        .Range("A1:F1").Merge
        .Range("A1").HorizontalAlignment = xlCenter
        .Range("A1").Interior.Color = RGB(68, 114, 196)
        .Range("A1").Font.Color = RGB(255, 255, 255)

        ' أزرار كشوف الحسابات
        Call AddAccountStatementButtons(ws)
    End With
End Sub

Sub AddAccountStatementButtons(ws As Worksheet)
    Dim btn As Shape
    Dim btnTop As Integer

    btnTop = 80

    ' كشف حساب عميل
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "كشف حساب عميل"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(0, 176, 80)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateCustomerStatement"
    End With

    ' كشف حساب مورد
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 220, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "كشف حساب مورد"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 192, 0)
        .TextFrame.Characters.Font.Color = RGB(0, 0, 0)
        .OnAction = "GenerateSupplierStatement"
    End With

    btnTop = btnTop + 60

    ' أعمار الديون
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 50, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "أعمار الديون"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(255, 0, 0)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateAgingReport"
    End With

    ' العملاء المتأخرين
    Set btn = ws.Shapes.AddShape(msoShapeRoundedRectangle, 220, btnTop, 150, 40)
    With btn
        .TextFrame.Characters.Text = "العملاء المتأخرين"
        .TextFrame.Characters.Font.Bold = True
        .Fill.ForeColor.RGB = RGB(128, 0, 128)
        .TextFrame.Characters.Font.Color = RGB(255, 255, 255)
        .OnAction = "GenerateOverdueCustomers"
    End With
End Sub

' كشف حساب عميل
Sub GenerateCustomerStatement()
    Dim customerCode As String
    Dim ws As Worksheet, reportWs As Worksheet
    Dim lastRow As Long, i As Long, reportRow As Long
    Dim customerName As String
    Dim totalBalance As Double

    customerCode = InputBox("كود العميل:", "كشف حساب عميل")
    If customerCode = "" Then Exit Sub

    customerName = GetCustomerName(customerCode)
    If customerName = "" Then
        MsgBox "العميل غير موجود", vbExclamation
        Exit Sub
    End If

    Set ws = ThisWorkbook.Worksheets("فواتير_المبيعات")
    Set reportWs = ThisWorkbook.Worksheets("كشف_الحسابات")

    ' مسح التقرير السابق
    reportWs.Range("A10:Z100").Clear

    ' عنوان التقرير
    reportWs.Range("A10").Value = "كشف حساب العميل: " & customerName
    reportWs.Range("A10").Font.Bold = True
    reportWs.Range("A10").Font.Size = 14

    reportWs.Range("A11").Value = "كود العميل: " & customerCode
    reportWs.Range("A11").Font.Bold = True

    ' عناوين الأعمدة
    reportWs.Range("A13").Value = "رقم الفاتورة"
    reportWs.Range("B13").Value = "التاريخ"
    reportWs.Range("C13").Value = "الإجمالي"
    reportWs.Range("D13").Value = "المدفوع"
    reportWs.Range("E13").Value = "المتبقي"
    reportWs.Range("F13").Value = "الحالة"
    reportWs.Range("A13:F13").Font.Bold = True
    reportWs.Range("A13:F13").Interior.Color = RGB(217, 217, 217)

    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    reportRow = 14

    For i = 2 To lastRow
        If ws.Cells(i, 3).Value = customerCode Then
            reportWs.Cells(reportRow, 1).Value = ws.Cells(i, 1).Value
            reportWs.Cells(reportRow, 2).Value = ws.Cells(i, 2).Value
            reportWs.Cells(reportRow, 3).Value = ws.Cells(i, 7).Value
            reportWs.Cells(reportRow, 4).Value = ws.Cells(i, 8).Value
            reportWs.Cells(reportRow, 5).Value = ws.Cells(i, 9).Value
            reportWs.Cells(reportRow, 6).Value = ws.Cells(i, 11).Value

            totalBalance = totalBalance + ws.Cells(i, 9).Value
            reportRow = reportRow + 1
        End If
    Next i

    ' إجمالي الرصيد
    reportWs.Cells(reportRow + 1, 4).Value = "إجمالي الرصيد:"
    reportWs.Cells(reportRow + 1, 5).Value = totalBalance
    reportWs.Cells(reportRow + 1, 5).Font.Bold = True
    reportWs.Cells(reportRow + 1, 5).NumberFormat = "#,##0.00 ""ج.م"""

    If totalBalance > 0 Then
        reportWs.Cells(reportRow + 1, 5).Font.Color = RGB(255, 0, 0)
    Else
        reportWs.Cells(reportRow + 1, 5).Font.Color = RGB(0, 128, 0)
    End If

    MsgBox "تم إنشاء كشف حساب العميل بنجاح", vbInformation
End Sub

' كشف حساب مورد
Sub GenerateSupplierStatement()
    Dim supplierCode As String
    Dim ws As Worksheet, reportWs As Worksheet
    Dim lastRow As Long, i As Long, reportRow As Long
    Dim supplierName As String
    Dim totalBalance As Double

    supplierCode = InputBox("كود المورد:", "كشف حساب مورد")
    If supplierCode = "" Then Exit Sub

    supplierName = GetSupplierName(supplierCode)
    If supplierName = "" Then
        MsgBox "المورد غير موجود", vbExclamation
        Exit Sub
    End If

    Set ws = ThisWorkbook.Worksheets("فواتير_المشتريات")
    Set reportWs = ThisWorkbook.Worksheets("كشف_الحسابات")

    ' مسح التقرير السابق
    reportWs.Range("A10:Z100").Clear

    ' عنوان التقرير
    reportWs.Range("A10").Value = "كشف حساب المورد: " & supplierName
    reportWs.Range("A10").Font.Bold = True
    reportWs.Range("A10").Font.Size = 14

    reportWs.Range("A11").Value = "كود المورد: " & supplierCode
    reportWs.Range("A11").Font.Bold = True

    ' عناوين الأعمدة
    reportWs.Range("A13").Value = "رقم الفاتورة"
    reportWs.Range("B13").Value = "التاريخ"
    reportWs.Range("C13").Value = "الإجمالي"
    reportWs.Range("D13").Value = "المدفوع"
    reportWs.Range("E13").Value = "المتبقي"
    reportWs.Range("F13").Value = "الحالة"
    reportWs.Range("A13:F13").Font.Bold = True
    reportWs.Range("A13:F13").Interior.Color = RGB(217, 217, 217)

    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    reportRow = 14

    For i = 2 To lastRow
        If ws.Cells(i, 3).Value = supplierCode Then
            reportWs.Cells(reportRow, 1).Value = ws.Cells(i, 1).Value
            reportWs.Cells(reportRow, 2).Value = ws.Cells(i, 2).Value
            reportWs.Cells(reportRow, 3).Value = ws.Cells(i, 7).Value
            reportWs.Cells(reportRow, 4).Value = ws.Cells(i, 8).Value
            reportWs.Cells(reportRow, 5).Value = ws.Cells(i, 9).Value
            reportWs.Cells(reportRow, 6).Value = ws.Cells(i, 11).Value

            totalBalance = totalBalance + ws.Cells(i, 9).Value
            reportRow = reportRow + 1
        End If
    Next i

    ' إجمالي الرصيد
    reportWs.Cells(reportRow + 1, 4).Value = "إجمالي المستحقات:"
    reportWs.Cells(reportRow + 1, 5).Value = totalBalance
    reportWs.Cells(reportRow + 1, 5).Font.Bold = True
    reportWs.Cells(reportRow + 1, 5).NumberFormat = "#,##0.00 ""ج.م"""

    If totalBalance > 0 Then
        reportWs.Cells(reportRow + 1, 5).Font.Color = RGB(255, 0, 0)
    Else
        reportWs.Cells(reportRow + 1, 5).Font.Color = RGB(0, 128, 0)
    End If

    MsgBox "تم إنشاء كشف حساب المورد بنجاح", vbInformation
End Sub

' تقرير أعمار الديون
Sub GenerateAgingReport()
    Dim ws As Worksheet, reportWs As Worksheet
    Dim lastRow As Long, i As Long, reportRow As Long
    Dim invoiceDate As Date, daysDiff As Long
    Dim current As Double, days30 As Double, days60 As Double, days90 As Double, over90 As Double

    Set ws = ThisWorkbook.Worksheets("فواتير_المبيعات")
    Set reportWs = ThisWorkbook.Worksheets("كشف_الحسابات")

    ' مسح التقرير السابق
    reportWs.Range("A10:Z100").Clear

    ' عنوان التقرير
    reportWs.Range("A10").Value = "تقرير أعمار الديون - " & Format(Date, "dd/mm/yyyy")
    reportWs.Range("A10").Font.Bold = True
    reportWs.Range("A10").Font.Size = 14

    ' عناوين الأعمدة
    reportWs.Range("A12").Value = "العميل"
    reportWs.Range("B12").Value = "جاري (0-30)"
    reportWs.Range("C12").Value = "30-60 يوم"
    reportWs.Range("D12").Value = "60-90 يوم"
    reportWs.Range("E12").Value = "أكثر من 90 يوم"
    reportWs.Range("F12").Value = "الإجمالي"
    reportWs.Range("A12:F12").Font.Bold = True
    reportWs.Range("A12:F12").Interior.Color = RGB(217, 217, 217)

    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    reportRow = 13

    ' تجميع البيانات حسب العميل
    Dim customerData As Object
    Set customerData = CreateObject("Scripting.Dictionary")

    For i = 2 To lastRow
        If ws.Cells(i, 9).Value > 0 Then ' المتبقي أكبر من صفر
            invoiceDate = ws.Cells(i, 2).Value
            daysDiff = Date - invoiceDate

            Dim customerName As String
            customerName = ws.Cells(i, 4).Value

            If Not customerData.Exists(customerName) Then
                customerData(customerName) = Array(0, 0, 0, 0, 0) ' current, 30, 60, 90, total
            End If

            Dim amounts As Variant
            amounts = customerData(customerName)

            If daysDiff <= 30 Then
                amounts(0) = amounts(0) + ws.Cells(i, 9).Value
            ElseIf daysDiff <= 60 Then
                amounts(1) = amounts(1) + ws.Cells(i, 9).Value
            ElseIf daysDiff <= 90 Then
                amounts(2) = amounts(2) + ws.Cells(i, 9).Value
            Else
                amounts(3) = amounts(3) + ws.Cells(i, 9).Value
            End If
            amounts(4) = amounts(4) + ws.Cells(i, 9).Value

            customerData(customerName) = amounts
        End If
    Next i

    ' عرض النتائج
    Dim key As Variant
    For Each key In customerData.Keys
        Dim data As Variant
        data = customerData(key)

        reportWs.Cells(reportRow, 1).Value = key
        reportWs.Cells(reportRow, 2).Value = data(0)
        reportWs.Cells(reportRow, 3).Value = data(1)
        reportWs.Cells(reportRow, 4).Value = data(2)
        reportWs.Cells(reportRow, 5).Value = data(3)
        reportWs.Cells(reportRow, 6).Value = data(4)

        ' تنسيق الأرقام
        reportWs.Range("B" & reportRow & ":F" & reportRow).NumberFormat = "#,##0.00 ""ج.م"""

        reportRow = reportRow + 1
    Next key

    MsgBox "تم إنشاء تقرير أعمار الديون بنجاح", vbInformation
End Sub
