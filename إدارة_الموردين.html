<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #e67e22;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #d35400;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-box {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            max-width: 300px;
        }

        .suppliers-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #e67e22;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .payment-terms {
            background: #3498db;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .payment-terms.cash {
            background: #27ae60;
        }

        .payment-terms.credit {
            background: #f39c12;
        }

        .status-active {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .status-inactive {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .action-btns {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #e67e22;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #e67e22;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 إدارة الموردين</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number" id="totalSuppliers">3</div>
                <div class="stat-label">إجمالي الموردين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeSuppliers">3</div>
                <div class="stat-label">الموردين النشطين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPayables">0</div>
                <div class="stat-label">إجمالي المستحقات (ج.م)</div>
            </div>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="openAddSupplierModal()">
                    ➕ إضافة مورد جديد
                </button>
                <button class="btn btn-primary" onclick="exportSuppliers()">
                    📊 تصدير البيانات
                </button>
                <button class="btn btn-info" onclick="generateSuppliersReport()">
                    📋 تقرير الموردين
                </button>
                <input type="text" class="search-box" placeholder="🔍 البحث عن مورد..." onkeyup="searchSuppliers(this.value)">
            </div>
        </div>

        <div class="suppliers-table">
            <div class="table-header">
                📋 قائمة الموردين
            </div>
            <table id="suppliersTable">
                <thead>
                    <tr>
                        <th>كود المورد</th>
                        <th>اسم المورد</th>
                        <th>التليفون</th>
                        <th>الموبايل</th>
                        <th>العنوان</th>
                        <th>شروط الدفع</th>
                        <th>الرصيد الحالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="suppliersTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل مورد -->
    <div id="supplierModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة مورد جديد</h2>
                <span class="close" onclick="closeSupplierModal()">&times;</span>
            </div>
            <form id="supplierForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierCode">كود المورد:</label>
                        <input type="text" id="supplierCode" readonly>
                    </div>
                    <div class="form-group">
                        <label for="supplierName">اسم المورد: *</label>
                        <input type="text" id="supplierName" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="supplierAddress">العنوان:</label>
                    <textarea id="supplierAddress" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierPhone">التليفون:</label>
                        <input type="tel" id="supplierPhone">
                    </div>
                    <div class="form-group">
                        <label for="supplierMobile">الموبايل:</label>
                        <input type="tel" id="supplierMobile">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierEmail">البريد الإلكتروني:</label>
                        <input type="email" id="supplierEmail">
                    </div>
                    <div class="form-group">
                        <label for="supplierTaxNumber">الرقم الضريبي:</label>
                        <input type="text" id="supplierTaxNumber">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="paymentTerms">شروط الدفع:</label>
                        <select id="paymentTerms">
                            <option value="نقدي">نقدي</option>
                            <option value="آجل 15 يوم">آجل 15 يوم</option>
                            <option value="آجل 30 يوم">آجل 30 يوم</option>
                            <option value="آجل 45 يوم">آجل 45 يوم</option>
                            <option value="آجل 60 يوم">آجل 60 يوم</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="supplierStatus">الحالة:</label>
                        <select id="supplierStatus">
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="supplierNotes">ملاحظات:</label>
                    <textarea id="supplierNotes" rows="3"></textarea>
                </div>
                
                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ</button>
                    <button type="button" class="btn btn-danger" onclick="closeSupplierModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // بيانات الموردين
        let suppliers = [
            {
                code: 'S0001',
                name: 'مطبعة الفجر',
                address: 'القاهرة - شبرا',
                phone: '0224567890',
                mobile: '01045678901',
                email: '<EMAIL>',
                taxNumber: '*********',
                paymentTerms: 'آجل 30 يوم',
                currentBalance: 0,
                status: 'نشط',
                notes: 'مورد أساسي للطباعة',
                registrationDate: '2024-01-10'
            },
            {
                code: 'S0002',
                name: 'شركة الورق المصرية',
                address: 'القاهرة - العباسية',
                phone: '0225678901',
                mobile: '01156789012',
                email: '<EMAIL>',
                taxNumber: '*********',
                paymentTerms: 'نقدي',
                currentBalance: 0,
                status: 'نشط',
                notes: 'مورد موثوق للورق',
                registrationDate: '2024-01-12'
            },
            {
                code: 'S0003',
                name: 'مصنع الأحبار الحديثة',
                address: 'الجيزة - إمبابة',
                phone: '0236789012',
                mobile: '01267890123',
                email: '<EMAIL>',
                taxNumber: '*********',
                paymentTerms: 'آجل 15 يوم',
                currentBalance: 0,
                status: 'نشط',
                notes: 'مورد جديد للأحبار',
                registrationDate: '2024-01-18'
            }
        ];

        let editingSupplierIndex = -1;

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadSuppliers();
            updateStats();
        });

        function loadSuppliers() {
            const tbody = document.getElementById('suppliersTableBody');
            tbody.innerHTML = '';

            suppliers.forEach((supplier, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${supplier.code}</td>
                    <td>${supplier.name}</td>
                    <td>${supplier.phone}</td>
                    <td>${supplier.mobile}</td>
                    <td>${supplier.address}</td>
                    <td>
                        <span class="payment-terms ${supplier.paymentTerms === 'نقدي' ? 'cash' : 'credit'}">
                            ${supplier.paymentTerms}
                        </span>
                    </td>
                    <td style="color: ${supplier.currentBalance > 0 ? '#e74c3c' : '#27ae60'}">
                        ${supplier.currentBalance.toLocaleString()} ج.م
                    </td>
                    <td>
                        <span class="status-${supplier.status === 'نشط' ? 'active' : 'inactive'}">
                            ${supplier.status}
                        </span>
                    </td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-warning" onclick="editSupplier(${index})">✏️ تعديل</button>
                            <button class="action-btn btn-primary" onclick="viewSupplierStatement(${index})">📊 كشف حساب</button>
                            <button class="action-btn btn-danger" onclick="deleteSupplier(${index})">🗑️ حذف</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateStats() {
            const totalSuppliers = suppliers.length;
            const activeSuppliers = suppliers.filter(s => s.status === 'نشط').length;
            const totalPayables = suppliers.reduce((sum, s) => sum + s.currentBalance, 0);

            document.getElementById('totalSuppliers').textContent = totalSuppliers;
            document.getElementById('activeSuppliers').textContent = activeSuppliers;
            document.getElementById('totalPayables').textContent = totalPayables.toLocaleString();
        }

        function openAddSupplierModal() {
            document.getElementById('modalTitle').textContent = 'إضافة مورد جديد';
            document.getElementById('supplierCode').value = generateSupplierCode();
            document.getElementById('supplierForm').reset();
            document.getElementById('supplierCode').value = generateSupplierCode();
            editingSupplierIndex = -1;
            document.getElementById('supplierModal').style.display = 'block';
        }

        function editSupplier(index) {
            const supplier = suppliers[index];
            document.getElementById('modalTitle').textContent = 'تعديل بيانات المورد';
            
            document.getElementById('supplierCode').value = supplier.code;
            document.getElementById('supplierName').value = supplier.name;
            document.getElementById('supplierAddress').value = supplier.address;
            document.getElementById('supplierPhone').value = supplier.phone;
            document.getElementById('supplierMobile').value = supplier.mobile;
            document.getElementById('supplierEmail').value = supplier.email;
            document.getElementById('supplierTaxNumber').value = supplier.taxNumber;
            document.getElementById('paymentTerms').value = supplier.paymentTerms;
            document.getElementById('supplierStatus').value = supplier.status;
            document.getElementById('supplierNotes').value = supplier.notes;
            
            editingSupplierIndex = index;
            document.getElementById('supplierModal').style.display = 'block';
        }

        function closeSupplierModal() {
            document.getElementById('supplierModal').style.display = 'none';
            document.getElementById('supplierForm').reset();
            editingSupplierIndex = -1;
        }

        function generateSupplierCode() {
            const lastCode = suppliers.length > 0 ? 
                Math.max(...suppliers.map(s => parseInt(s.code.substring(1)))) : 0;
            return 'S' + String(lastCode + 1).padStart(4, '0');
        }

        // حفظ بيانات المورد
        document.getElementById('supplierForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const supplierData = {
                code: document.getElementById('supplierCode').value,
                name: document.getElementById('supplierName').value,
                address: document.getElementById('supplierAddress').value,
                phone: document.getElementById('supplierPhone').value,
                mobile: document.getElementById('supplierMobile').value,
                email: document.getElementById('supplierEmail').value,
                taxNumber: document.getElementById('supplierTaxNumber').value,
                paymentTerms: document.getElementById('paymentTerms').value,
                currentBalance: editingSupplierIndex >= 0 ? suppliers[editingSupplierIndex].currentBalance : 0,
                status: document.getElementById('supplierStatus').value,
                notes: document.getElementById('supplierNotes').value,
                registrationDate: editingSupplierIndex >= 0 ? suppliers[editingSupplierIndex].registrationDate : new Date().toISOString().split('T')[0]
            };

            if (editingSupplierIndex >= 0) {
                suppliers[editingSupplierIndex] = supplierData;
                alert('تم تحديث بيانات المورد بنجاح');
            } else {
                suppliers.push(supplierData);
                alert('تم إضافة المورد بنجاح');
            }

            loadSuppliers();
            updateStats();
            closeSupplierModal();
        });

        function deleteSupplier(index) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
                suppliers.splice(index, 1);
                loadSuppliers();
                updateStats();
                alert('تم حذف المورد بنجاح');
            }
        }

        function searchSuppliers(searchTerm) {
            const tbody = document.getElementById('suppliersTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function viewSupplierStatement(index) {
            const supplier = suppliers[index];
            alert(`كشف حساب المورد: ${supplier.name}\n\nالرصيد الحالي: ${supplier.currentBalance.toLocaleString()} ج.م\nشروط الدفع: ${supplier.paymentTerms}\n\nسيتم فتح التقرير المفصل قريباً...`);
        }

        function exportSuppliers() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "كود المورد,اسم المورد,العنوان,التليفون,الموبايل,البريد الإلكتروني,الرقم الضريبي,شروط الدفع,الرصيد الحالي,الحالة,ملاحظات\n"
                + suppliers.map(s => `${s.code},${s.name},${s.address},${s.phone},${s.mobile},${s.email},${s.taxNumber},${s.paymentTerms},${s.currentBalance},${s.status},${s.notes}`).join("\n");
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "موردين_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateSuppliersReport() {
            alert('تقرير الموردين:\n\n' +
                  `إجمالي الموردين: ${suppliers.length}\n` +
                  `الموردين النشطين: ${suppliers.filter(s => s.status === 'نشط').length}\n` +
                  `الموردين غير النشطين: ${suppliers.filter(s => s.status === 'غير نشط').length}\n` +
                  `إجمالي المستحقات: ${suppliers.reduce((sum, s) => sum + s.currentBalance, 0).toLocaleString()} ج.م\n\n` +
                  'سيتم إنشاء تقرير مفصل قريباً...');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('supplierModal');
            if (event.target == modal) {
                closeSupplierModal();
            }
        }
    </script>
</body>
</html>
