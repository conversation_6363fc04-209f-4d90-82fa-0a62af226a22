# نظام نيو جرافيك المحاسبي المتكامل

## شركة نيو جرافيك للدعاية والإعلان

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](VERSION)
[![Language](https://img.shields.io/badge/language-Arabic-red.svg)](README.md)

---

## 📋 نظرة عامة

نظام محاسبي متكامل مصمم خصيصاً لشركات الدعاية والإعلان والمطبوعات. يوفر النظام حلولاً شاملة لإدارة العملاء، الموردين، المخزون، الفواتير، والتقارير المالية بواجهة عربية بالكامل.

## 🎯 المميزات الرئيسية

### 💼 إدارة شاملة
- **إدارة العملاء والموردين** مع تتبع كامل للحسابات
- **إدارة المخزون والأصناف** مع تنبيهات الحد الأدنى
- **نظام فواتير متطور** للمبيعات والمشتريات
- **تتبع المصروفات والإيرادات** بتصنيفات مفصلة

### 📊 تقارير وتحليلات
- **لوحة تحكم ذكية** (Dashboard) مع ملخص فوري
- **تقارير مالية شاملة** (أرباح وخسائر، مبيعات، مشتريات)
- **كشوف حسابات مفصلة** للعملاء والموردين
- **تحليل الأعمار** وتقييم المخاطر الائتمانية

### 🔒 الأمان والصلاحيات
- **نظام صلاحيات متقدم** مع مستويات مختلفة
- **تسجيل عمليات المستخدمين** وتتبع النشاطات
- **حماية البيانات** مع نسخ احتياطي تلقائي

### 🌐 واجهة المستخدم
- **واجهة عربية بالكامل** سهلة الاستخدام
- **تصميم احترافي** مناسب لبيئة العمل
- **تنقل سريع** بين الوحدات المختلفة

## 🏗️ هيكل النظام

```
نظام_نيو_جرافيك_المحاسبي/
├── نظام_نيو_جرافيك_المحاسبي.xlsm     # نظام Excel VBA
├── نظام_نيو_جرافيك_Access.accdb        # نظام Access
├── دليل_المستخدم_نظام_نيو_جرافيك.md    # دليل المستخدم
├── README.md                           # ملف التوثيق
└── docs/                              # مجلد التوثيق الإضافي
```

## 💻 متطلبات النظام

### نظام Excel VBA
- Microsoft Excel 2016 أو أحدث
- Windows 10 أو أحدث
- تفعيل الماكرو (Macros)
- ذاكرة وصول عشوائي 4 جيجابايت على الأقل

### نظام Access
- Microsoft Access 2016 أو أحدث
- .NET Framework 4.7 أو أحدث
- ذاكرة وصول عشوائي 4 جيجابايت على الأقل
- مساحة تخزين 500 ميجابايت على الأقل

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/newgraphic/accounting-system.git
cd accounting-system
```

### 2. إعداد نظام Excel
1. افتح ملف `نظام_نيو_جرافيك_المحاسبي.xlsm`
2. فعّل الماكرو عند السؤال
3. شغّل الدالة `InitializeSystem()` لإعداد النظام
4. استخدم المستخدم الافتراضي: `admin` / `123456`

### 3. إعداد نظام Access
1. افتح ملف `نظام_نيو_جرافيك_Access.accdb`
2. شغّل الاستعلامات لإنشاء الجداول
3. أدخل البيانات الأساسية
4. اختبر النماذج والتقارير

## 📚 الوحدات الرئيسية

### 🏠 لوحة التحكم
- ملخص مالي فوري
- آخر العمليات
- مؤشرات الأداء الرئيسية
- أزرار التنقل السريع

### 👥 إدارة العملاء
```vba
' إضافة عميل جديد
Sub AddNewCustomer()
    ' كود إضافة العميل
End Sub

' تحديث رصيد العميل
Sub UpdateCustomerBalance(customerCode As String, amount As Double)
    ' كود تحديث الرصيد
End Sub
```

### 🏭 إدارة الموردين
- تسجيل بيانات الموردين
- تتبع المشتريات والمدفوعات
- تقييم أداء الموردين

### 📦 إدارة الأصناف
- كتالوج شامل للمنتجات والخدمات
- تتبع المخزون والحركة
- تنبيهات الحد الأدنى

### 🧾 نظام الفواتير
```vba
' إنشاء فاتورة مبيعات
Sub CreateNewSalesInvoice()
    ' كود إنشاء الفاتورة
    ' حساب الضريبة تلقائياً
    ' تحديث رصيد العميل
End Sub
```

### 📊 التقارير المالية
- تقرير الأرباح والخسائر
- تقارير المبيعات والمشتريات
- تحليل الاتجاهات المالية

## 🔧 التخصيص والتطوير

### إضافة وحدات جديدة
```vba
' قالب لوحدة جديدة
Sub Navigate_وحدة_جديدة()
    Call SetupNewModuleSheet
    ThisWorkbook.Worksheets("الوحدة_الجديدة").Activate
End Sub
```

### تخصيص التقارير
```sql
-- استعلام مخصص للتقارير
CREATE QUERY استعلام_مخصص AS
SELECT 
    العميل,
    Sum(المبلغ) AS الإجمالي
FROM المعاملات
WHERE التاريخ BETWEEN [تاريخ_البداية] AND [تاريخ_النهاية]
GROUP BY العميل;
```

### إضافة حقول جديدة
1. أضف الحقل في ورقة البيانات
2. حدّث النماذج المرتبطة
3. عدّل التقارير حسب الحاجة

## 🧪 الاختبار

### اختبار الوحدات
```vba
' اختبار إضافة عميل
Sub TestAddCustomer()
    Call AddNewCustomer()
    ' التحقق من صحة البيانات
End Sub
```

### اختبار التقارير
- تشغيل جميع التقارير
- التحقق من صحة الحسابات
- اختبار الفلاتر والمعايير

## 📈 الأداء والتحسين

### نصائح للأداء الأمثل
- استخدم الفهارس في قواعد البيانات
- قم بضغط الملفات دورياً
- احذف البيانات القديمة غير المطلوبة
- استخدم الاستعلامات المحسّنة

### مراقبة الأداء
```vba
' قياس وقت تنفيذ العمليات
Sub MeasurePerformance()
    Dim startTime As Double
    startTime = Timer
    
    ' العملية المراد قياسها
    Call SomeOperation()
    
    Debug.Print "وقت التنفيذ: " & (Timer - startTime) & " ثانية"
End Sub
```

## 🔒 الأمان

### حماية البيانات
- تشفير كلمات المرور
- نسخ احتياطي تلقائي
- تسجيل عمليات المستخدمين

### أفضل الممارسات الأمنية
```vba
' تشفير كلمة المرور
Function EncryptPassword(password As String) As String
    ' كود التشفير
End Function

' التحقق من الصلاحيات
Function CheckUserPermission(action As String) As Boolean
    ' كود التحقق من الصلاحيات
End Function
```

## 🐛 استكشاف الأخطاء

### الأخطاء الشائعة
1. **خطأ في تسجيل الدخول:** تحقق من اسم المستخدم وكلمة المرور
2. **بطء في النظام:** أغلق البرامج الأخرى وأعد التشغيل
3. **خطأ في الحفظ:** تأكد من وجود مساحة كافية

### تسجيل الأخطاء
```vba
' تسجيل الأخطاء
Sub LogError(errorMessage As String)
    Dim logFile As String
    logFile = ThisWorkbook.Path & "\error_log.txt"
    
    Open logFile For Append As #1
    Print #1, Now() & " - " & errorMessage
    Close #1
End Sub
```

## 📞 الدعم والمساهمة

### الإبلاغ عن المشاكل
- استخدم نظام Issues في GitHub
- قدم وصفاً مفصلاً للمشكلة
- أرفق لقطات شاشة إن أمكن

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التوثيق
4. أرسل Pull Request

### معايير الكود
- استخدم أسماء متغيرات واضحة بالعربية
- اكتب تعليقات مفصلة
- اتبع نمط الكود الموحد
- اختبر الكود قبل الإرسال

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطورون

- **الفريق الرئيسي:** شركة نيو جرافيك للدعاية والإعلان
- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** www.newgraphic.com

## 🙏 شكر وتقدير

نشكر جميع المساهمين في تطوير هذا النظام والمجتمع المفتوح المصدر.

---

**© 2024 شركة نيو جرافيك للدعاية والإعلان - جميع الحقوق محفوظة**
