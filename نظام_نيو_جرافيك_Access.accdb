' نظام نيو جرافيك المحاسبي - Microsoft Access
' شركة نيو جرافيك للدعاية والإعلان
' قاعدة البيانات المحاسبية المتكاملة

' ==== إنشاء الجداول الأساسية ====

' جدول العملاء
CREATE TABLE العملاء (
    كود_العميل AUTOINCREMENT PRIMARY KEY,
    اسم_العميل TEXT(100) NOT NULL,
    العنوان TEXT(255),
    التليفون TEXT(20),
    الموبايل TEXT(20),
    البريد_الإلكتروني TEXT(100),
    الرقم_الضريبي TEXT(50),
    حد_الائتمان CURRENCY DEFAULT 0,
    الرصيد_الحالي CURRENCY DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    ملاحظا<PERSON> MEMO,
    نشط YES/NO DEFAULT True
);

' جدول الموردين
CREATE TABLE الموردين (
    كود_المورد AUTOINCREMENT PRIMARY KEY,
    اسم_المورد TEXT(100) NOT NULL,
    العنوان TEXT(255),
    التليفون TEXT(20),
    الموبايل TEXT(20),
    البريد_الإلكتروني TEXT(100),
    الرقم_الضريبي TEXT(50),
    شروط_الدفع TEXT(50) DEFAULT "نقدي",
    الرصيد_الحالي CURRENCY DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    ملاحظات MEMO,
    نشط YES/NO DEFAULT True
);

' جدول الأصناف
CREATE TABLE الأصناف (
    كود_الصنف AUTOINCREMENT PRIMARY KEY,
    اسم_الصنف TEXT(100) NOT NULL,
    الوصف TEXT(255),
    الوحدة TEXT(20) DEFAULT "قطعة",
    سعر_الشراء CURRENCY DEFAULT 0,
    سعر_البيع CURRENCY DEFAULT 0,
    الكمية_المتاحة DOUBLE DEFAULT 0,
    الحد_الأدنى DOUBLE DEFAULT 0,
    المجموعة TEXT(50) DEFAULT "مطبوعات",
    تاريخ_الإضافة DATETIME DEFAULT Now(),
    ملاحظات MEMO,
    نشط YES/NO DEFAULT True
);

' جدول فواتير المبيعات
CREATE TABLE فواتير_المبيعات (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    التاريخ DATETIME DEFAULT Now(),
    كود_العميل LONG,
    إجمالي_قبل_الضريبة CURRENCY DEFAULT 0,
    الضريبة CURRENCY DEFAULT 0,
    الإجمالي_النهائي CURRENCY DEFAULT 0,
    المدفوع CURRENCY DEFAULT 0,
    المتبقي CURRENCY DEFAULT 0,
    طريقة_الدفع TEXT(50) DEFAULT "نقدي",
    الحالة TEXT(20) DEFAULT "مدفوعة",
    ملاحظات MEMO,
    المستخدم TEXT(50),
    FOREIGN KEY (كود_العميل) REFERENCES العملاء(كود_العميل)
);

' جدول تفاصيل فواتير المبيعات
CREATE TABLE تفاصيل_فواتير_المبيعات (
    رقم_التفصيل AUTOINCREMENT PRIMARY KEY,
    رقم_الفاتورة LONG,
    كود_الصنف LONG,
    الكمية DOUBLE DEFAULT 1,
    السعر CURRENCY DEFAULT 0,
    الإجمالي CURRENCY DEFAULT 0,
    ملاحظات TEXT(255),
    FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_المبيعات(رقم_الفاتورة),
    FOREIGN KEY (كود_الصنف) REFERENCES الأصناف(كود_الصنف)
);

' جدول فواتير المشتريات
CREATE TABLE فواتير_المشتريات (
    رقم_الفاتورة AUTOINCREMENT PRIMARY KEY,
    التاريخ DATETIME DEFAULT Now(),
    كود_المورد LONG,
    إجمالي_قبل_الضريبة CURRENCY DEFAULT 0,
    الضريبة CURRENCY DEFAULT 0,
    الإجمالي_النهائي CURRENCY DEFAULT 0,
    المدفوع CURRENCY DEFAULT 0,
    المتبقي CURRENCY DEFAULT 0,
    طريقة_الدفع TEXT(50) DEFAULT "نقدي",
    الحالة TEXT(20) DEFAULT "مدفوعة",
    ملاحظات MEMO,
    المستخدم TEXT(50),
    FOREIGN KEY (كود_المورد) REFERENCES الموردين(كود_المورد)
);

' جدول المصروفات
CREATE TABLE المصروفات (
    رقم_المصروف AUTOINCREMENT PRIMARY KEY,
    التاريخ DATETIME DEFAULT Now(),
    نوع_المصروف TEXT(100) NOT NULL,
    المبلغ CURRENCY NOT NULL,
    الوصف TEXT(255),
    طريقة_الدفع TEXT(50) DEFAULT "نقدي",
    المستخدم TEXT(50),
    ملاحظات MEMO
);

' جدول الإيرادات الأخرى
CREATE TABLE الإيرادات_الأخرى (
    رقم_الإيراد AUTOINCREMENT PRIMARY KEY,
    التاريخ DATETIME DEFAULT Now(),
    نوع_الإيراد TEXT(100) NOT NULL,
    المبلغ CURRENCY NOT NULL,
    الوصف TEXT(255),
    طريقة_الاستلام TEXT(50) DEFAULT "نقدي",
    المستخدم TEXT(50),
    ملاحظات MEMO
);

' جدول المستخدمين
CREATE TABLE المستخدمين (
    كود_المستخدم AUTOINCREMENT PRIMARY KEY,
    اسم_المستخدم TEXT(50) UNIQUE NOT NULL,
    كلمة_المرور TEXT(100) NOT NULL,
    نوع_الصلاحية TEXT(50) DEFAULT "مستخدم عادي",
    الاسم_الكامل TEXT(100),
    البريد_الإلكتروني TEXT(100),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    آخر_دخول DATETIME,
    نشط YES/NO DEFAULT True
);

' جدول الصلاحيات
CREATE TABLE الصلاحيات (
    رقم_الصلاحية AUTOINCREMENT PRIMARY KEY,
    كود_المستخدم LONG,
    اسم_النموذج TEXT(50),
    قراءة YES/NO DEFAULT False,
    كتابة YES/NO DEFAULT False,
    تعديل YES/NO DEFAULT False,
    حذف YES/NO DEFAULT False,
    FOREIGN KEY (كود_المستخدم) REFERENCES المستخدمين(كود_المستخدم)
);

' ==== إدراج البيانات الأساسية ====

' إدراج مستخدم افتراضي
INSERT INTO المستخدمين (اسم_المستخدم, كلمة_المرور, نوع_الصلاحية, الاسم_الكامل)
VALUES ('admin', '123456', 'مدير عام', 'مدير النظام');

' إدراج أصناف أساسية للمطبوعات
INSERT INTO الأصناف (اسم_الصنف, الوصف, الوحدة, المجموعة)
VALUES 
('بروشور A4', 'بروشور مطبوع على ورق A4', 'قطعة', 'مطبوعات'),
('فلاير A5', 'فلاير إعلاني مقاس A5', 'قطعة', 'مطبوعات'),
('بانر إعلاني', 'بانر إعلاني للأحداث', 'متر مربع', 'إعلانات خارجية'),
('كارت شخصي', 'كارت شخصي مطبوع', 'علبة 100 قطعة', 'مطبوعات'),
('ستيكر', 'ستيكر إعلاني', 'قطعة', 'مطبوعات');

' إدراج عملاء تجريبيين
INSERT INTO العملاء (اسم_العميل, العنوان, التليفون, الموبايل)
VALUES 
('شركة الأهرام للتجارة', 'القاهرة - مصر الجديدة', '0223456789', '01012345678'),
('مؤسسة النيل للخدمات', 'الجيزة - المهندسين', '0233456789', '01123456789'),
('شركة الدلتا للصناعات', 'الإسكندرية - سموحة', '0334567890', '01234567890');

' إدراج موردين تجريبيين
INSERT INTO الموردين (اسم_المورد, العنوان, التليفون, شروط_الدفع)
VALUES 
('مطبعة الفجر', 'القاهرة - شبرا', '0224567890', 'آجل 30 يوم'),
('شركة الورق المصرية', 'القاهرة - العباسية', '0225678901', 'نقدي'),
('مصنع الأحبار الحديثة', 'الجيزة - إمبابة', '0236789012', 'آجل 15 يوم');

' ==== الاستعلامات المحفوظة ====

' استعلام إجمالي المبيعات الشهرية
CREATE QUERY استعلام_المبيعات_الشهرية AS
SELECT 
    Format([التاريخ],"yyyy-mm") AS الشهر,
    Sum([الإجمالي_النهائي]) AS إجمالي_المبيعات,
    Count(*) AS عدد_الفواتير
FROM فواتير_المبيعات
GROUP BY Format([التاريخ],"yyyy-mm")
ORDER BY Format([التاريخ],"yyyy-mm") DESC;

' استعلام العملاء المتأخرين في السداد
CREATE QUERY العملاء_المتأخرين AS
SELECT 
    ع.اسم_العميل,
    ع.التليفون,
    ع.الموبايل,
    Sum(ف.المتبقي) AS إجمالي_المتأخرات,
    Count(ف.رقم_الفاتورة) AS عدد_الفواتير_المتأخرة
FROM العملاء ع INNER JOIN فواتير_المبيعات ف ON ع.كود_العميل = ف.كود_العميل
WHERE ف.المتبقي > 0 AND ف.التاريخ < DateAdd("d", -30, Date())
GROUP BY ع.اسم_العميل, ع.التليفون, ع.الموبايل
ORDER BY Sum(ف.المتبقي) DESC;

' استعلام الأصناف الناقصة
CREATE QUERY الأصناف_الناقصة AS
SELECT 
    اسم_الصنف,
    الكمية_المتاحة,
    الحد_الأدنى,
    (الحد_الأدنى - الكمية_المتاحة) AS الكمية_المطلوبة
FROM الأصناف
WHERE الكمية_المتاحة <= الحد_الأدنى AND نشط = True
ORDER BY (الحد_الأدنى - الكمية_المتاحة) DESC;

' استعلام الأرباح والخسائر
CREATE QUERY الأرباح_والخسائر AS
SELECT 
    "المبيعات" AS النوع,
    Sum(الإجمالي_النهائي) AS المبلغ,
    Format([التاريخ],"yyyy-mm") AS الشهر
FROM فواتير_المبيعات
GROUP BY Format([التاريخ],"yyyy-mm")
UNION ALL
SELECT 
    "المشتريات" AS النوع,
    -Sum(الإجمالي_النهائي) AS المبلغ,
    Format([التاريخ],"yyyy-mm") AS الشهر
FROM فواتير_المشتريات
GROUP BY Format([التاريخ],"yyyy-mm")
UNION ALL
SELECT 
    "المصروفات" AS النوع,
    -Sum(المبلغ) AS المبلغ,
    Format([التاريخ],"yyyy-mm") AS الشهر
FROM المصروفات
GROUP BY Format([التاريخ],"yyyy-mm")
ORDER BY الشهر DESC, النوع;

' ==== الفهارس لتحسين الأداء ====
CREATE INDEX idx_فواتير_المبيعات_التاريخ ON فواتير_المبيعات (التاريخ);
CREATE INDEX idx_فواتير_المبيعات_العميل ON فواتير_المبيعات (كود_العميل);
CREATE INDEX idx_فواتير_المشتريات_التاريخ ON فواتير_المشتريات (التاريخ);
CREATE INDEX idx_فواتير_المشتريات_المورد ON فواتير_المشتريات (كود_المورد);
CREATE INDEX idx_المصروفات_التاريخ ON المصروفات (التاريخ);
CREATE INDEX idx_الإيرادات_التاريخ ON الإيرادات_الأخرى (التاريخ);

' ==== النماذج (Forms) ====

' نموذج تسجيل الدخول
CREATE FORM نموذج_تسجيل_الدخول AS
{
    Caption: "تسجيل الدخول - نظام نيو جرافيك المحاسبي",
    RecordSource: "",
    DefaultView: "Single Form",
    AllowAdditions: False,
    AllowDeletions: False,
    AllowEdits: False,
    NavigationButtons: False,
    RecordSelectors: False,
    DividingLines: False,

    Controls: [
        {
            Type: "Label",
            Name: "lblTitle",
            Caption: "شركة نيو جرافيك للدعاية والإعلان",
            FontSize: 18,
            FontBold: True,
            ForeColor: "Blue",
            TextAlign: "Center"
        },
        {
            Type: "Label",
            Name: "lblUsername",
            Caption: "اسم المستخدم:",
            FontSize: 12
        },
        {
            Type: "TextBox",
            Name: "txtUsername",
            FontSize: 12
        },
        {
            Type: "Label",
            Name: "lblPassword",
            Caption: "كلمة المرور:",
            FontSize: 12
        },
        {
            Type: "TextBox",
            Name: "txtPassword",
            InputMask: "Password",
            FontSize: 12
        },
        {
            Type: "CommandButton",
            Name: "btnLogin",
            Caption: "دخول",
            OnClick: "LoginUser()",
            FontSize: 12,
            FontBold: True
        },
        {
            Type: "CommandButton",
            Name: "btnCancel",
            Caption: "إلغاء",
            OnClick: "Application.Quit",
            FontSize: 12
        }
    ]
}

' نموذج لوحة التحكم الرئيسية
CREATE FORM نموذج_لوحة_التحكم AS
{
    Caption: "لوحة التحكم - نظام نيو جرافيك المحاسبي",
    RecordSource: "",
    DefaultView: "Single Form",
    AllowAdditions: False,
    AllowDeletions: False,
    AllowEdits: False,
    NavigationButtons: False,
    RecordSelectors: False,

    Controls: [
        {
            Type: "Label",
            Name: "lblCompanyName",
            Caption: "شركة نيو جرافيك للدعاية والإعلان",
            FontSize: 20,
            FontBold: True,
            ForeColor: "Blue",
            TextAlign: "Center"
        },
        {
            Type: "Label",
            Name: "lblCurrentDate",
            Caption: "التاريخ: " & Format(Date, "dd/mm/yyyy"),
            FontSize: 12
        },
        {
            Type: "Label",
            Name: "lblCurrentUser",
            Caption: "المستخدم الحالي: [CurrentUser]",
            FontSize: 12
        },
        {
            Type: "CommandButton",
            Name: "btnCustomers",
            Caption: "إدارة العملاء",
            OnClick: "OpenForm('نموذج_العملاء')",
            FontSize: 12,
            BackColor: "Green",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnSuppliers",
            Caption: "إدارة الموردين",
            OnClick: "OpenForm('نموذج_الموردين')",
            FontSize: 12,
            BackColor: "Orange",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnItems",
            Caption: "إدارة الأصناف",
            OnClick: "OpenForm('نموذج_الأصناف')",
            FontSize: 12,
            BackColor: "Purple",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnSalesInvoices",
            Caption: "فواتير المبيعات",
            OnClick: "OpenForm('نموذج_فواتير_المبيعات')",
            FontSize: 12,
            BackColor: "Blue",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnPurchaseInvoices",
            Caption: "فواتير المشتريات",
            OnClick: "OpenForm('نموذج_فواتير_المشتريات')",
            FontSize: 12,
            BackColor: "Red",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnExpenses",
            Caption: "المصروفات",
            OnClick: "OpenForm('نموذج_المصروفات')",
            FontSize: 12,
            BackColor: "Gray",
            ForeColor: "White"
        },
        {
            Type: "CommandButton",
            Name: "btnReports",
            Caption: "التقارير",
            OnClick: "OpenForm('نموذج_التقارير')",
            FontSize: 12,
            BackColor: "Navy",
            ForeColor: "White"
        },
        {
            Type: "Subform",
            Name: "subFinancialSummary",
            SourceObject: "نموذج_الملخص_المالي",
            LinkChildFields: "",
            LinkMasterFields: ""
        }
    ]
}

' نموذج إدارة العملاء
CREATE FORM نموذج_العملاء AS
{
    Caption: "إدارة العملاء",
    RecordSource: "العملاء",
    DefaultView: "Continuous Forms",
    AllowAdditions: True,
    AllowDeletions: True,
    AllowEdits: True,

    Controls: [
        {
            Type: "TextBox",
            Name: "txtCustomerCode",
            ControlSource: "كود_العميل",
            Caption: "كود العميل",
            Enabled: False
        },
        {
            Type: "TextBox",
            Name: "txtCustomerName",
            ControlSource: "اسم_العميل",
            Caption: "اسم العميل",
            Required: True
        },
        {
            Type: "TextBox",
            Name: "txtAddress",
            ControlSource: "العنوان",
            Caption: "العنوان"
        },
        {
            Type: "TextBox",
            Name: "txtPhone",
            ControlSource: "التليفون",
            Caption: "التليفون"
        },
        {
            Type: "TextBox",
            Name: "txtMobile",
            ControlSource: "الموبايل",
            Caption: "الموبايل"
        },
        {
            Type: "TextBox",
            Name: "txtEmail",
            ControlSource: "البريد_الإلكتروني",
            Caption: "البريد الإلكتروني"
        },
        {
            Type: "TextBox",
            Name: "txtTaxNumber",
            ControlSource: "الرقم_الضريبي",
            Caption: "الرقم الضريبي"
        },
        {
            Type: "TextBox",
            Name: "txtCreditLimit",
            ControlSource: "حد_الائتمان",
            Caption: "حد الائتمان",
            Format: "Currency"
        },
        {
            Type: "TextBox",
            Name: "txtCurrentBalance",
            ControlSource: "الرصيد_الحالي",
            Caption: "الرصيد الحالي",
            Format: "Currency",
            Enabled: False
        },
        {
            Type: "CheckBox",
            Name: "chkActive",
            ControlSource: "نشط",
            Caption: "نشط"
        },
        {
            Type: "CommandButton",
            Name: "btnNewCustomer",
            Caption: "عميل جديد",
            OnClick: "DoCmd.GoToRecord , , acNewRec"
        },
        {
            Type: "CommandButton",
            Name: "btnSaveCustomer",
            Caption: "حفظ",
            OnClick: "DoCmd.RunCommand acCmdSaveRecord"
        },
        {
            Type: "CommandButton",
            Name: "btnDeleteCustomer",
            Caption: "حذف",
            OnClick: "DeleteCurrentRecord()"
        },
        {
            Type: "CommandButton",
            Name: "btnCustomerStatement",
            Caption: "كشف حساب",
            OnClick: "OpenCustomerStatement()"
        }
    ]
}

' ==== كود VBA للنظام ====

' وحدة المتغيرات العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserPermissions As String

' دالة تسجيل الدخول
Function LoginUser() As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    Dim username As String
    Dim password As String

    Set db = CurrentDb

    username = Forms("نموذج_تسجيل_الدخول")("txtUsername").Value
    password = Forms("نموذج_تسجيل_الدخول")("txtPassword").Value

    If Len(username) = 0 Or Len(password) = 0 Then
        MsgBox "يرجى إدخال اسم المستخدم وكلمة المرور", vbExclamation, "خطأ"
        LoginUser = False
        Exit Function
    End If

    strSQL = "SELECT * FROM المستخدمين WHERE اسم_المستخدم = '" & username & "' AND كلمة_المرور = '" & password & "' AND نشط = True"
    Set rs = db.OpenRecordset(strSQL)

    If Not rs.EOF Then
        CurrentUserID = rs("كود_المستخدم")
        CurrentUserName = rs("اسم_المستخدم")
        CurrentUserPermissions = rs("نوع_الصلاحية")

        ' تحديث آخر دخول
        rs.Edit
        rs("آخر_دخول") = Now()
        rs.Update

        ' إغلاق نموذج تسجيل الدخول وفتح لوحة التحكم
        DoCmd.Close acForm, "نموذج_تسجيل_الدخول"
        DoCmd.OpenForm "نموذج_لوحة_التحكم"

        LoginUser = True
        MsgBox "مرحباً " & CurrentUserName, vbInformation, "تسجيل دخول ناجح"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbCritical, "خطأ في تسجيل الدخول"
        LoginUser = False
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة حذف السجل الحالي
Sub DeleteCurrentRecord()
    If MsgBox("هل أنت متأكد من حذف هذا السجل؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
        DoCmd.RunCommand acCmdSelectRecord
        DoCmd.RunCommand acCmdDeleteRecord
    End If
End Sub

' دالة فتح كشف حساب العميل
Sub OpenCustomerStatement()
    Dim customerCode As Long
    customerCode = Forms("نموذج_العملاء")("txtCustomerCode").Value

    If IsNull(customerCode) Then
        MsgBox "يرجى اختيار عميل أولاً", vbExclamation
        Exit Sub
    End If

    DoCmd.OpenReport "تقرير_كشف_حساب_العميل", acViewPreview, , "كود_العميل = " & customerCode
End Sub

' دالة حساب إجمالي الفاتورة
Function CalculateInvoiceTotal(invoiceNumber As Long, invoiceType As String) As Currency
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    Dim total As Currency

    Set db = CurrentDb

    If invoiceType = "مبيعات" Then
        strSQL = "SELECT Sum(الإجمالي) AS المجموع FROM تفاصيل_فواتير_المبيعات WHERE رقم_الفاتورة = " & invoiceNumber
    Else
        strSQL = "SELECT Sum(الإجمالي) AS المجموع FROM تفاصيل_فواتير_المشتريات WHERE رقم_الفاتورة = " & invoiceNumber
    End If

    Set rs = db.OpenRecordset(strSQL)

    If Not rs.EOF And Not IsNull(rs("المجموع")) Then
        total = rs("المجموع")
    Else
        total = 0
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    CalculateInvoiceTotal = total
End Function

' دالة تحديث رصيد العميل
Sub UpdateCustomerBalance(customerCode As Long, amount As Currency)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String

    Set db = CurrentDb
    strSQL = "SELECT * FROM العملاء WHERE كود_العميل = " & customerCode
    Set rs = db.OpenRecordset(strSQL)

    If Not rs.EOF Then
        rs.Edit
        rs("الرصيد_الحالي") = rs("الرصيد_الحالي") + amount
        rs.Update
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' دالة تحديث رصيد المورد
Sub UpdateSupplierBalance(supplierCode As Long, amount As Currency)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String

    Set db = CurrentDb
    strSQL = "SELECT * FROM الموردين WHERE كود_المورد = " & supplierCode
    Set rs = db.OpenRecordset(strSQL)

    If Not rs.EOF Then
        rs.Edit
        rs("الرصيد_الحالي") = rs("الرصيد_الحالي") + amount
        rs.Update
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' ==== التقارير (Reports) ====

' تقرير كشف حساب العميل
CREATE REPORT تقرير_كشف_حساب_العميل AS
{
    Caption: "كشف حساب العميل",
    RecordSource: "SELECT ف.رقم_الفاتورة, ف.التاريخ, ف.الإجمالي_النهائي, ف.المدفوع, ف.المتبقي, ع.اسم_العميل FROM فواتير_المبيعات ف INNER JOIN العملاء ع ON ف.كود_العميل = ع.كود_العميل",

    ReportHeader: [
        {
            Type: "Label",
            Name: "lblReportTitle",
            Caption: "كشف حساب العميل",
            FontSize: 16,
            FontBold: True,
            TextAlign: "Center"
        },
        {
            Type: "Label",
            Name: "lblCompanyName",
            Caption: "شركة نيو جرافيك للدعاية والإعلان",
            FontSize: 14,
            FontBold: True,
            TextAlign: "Center"
        },
        {
            Type: "Label",
            Name: "lblPrintDate",
            Caption: "تاريخ الطباعة: " & Format(Date, "dd/mm/yyyy"),
            FontSize: 10
        }
    ],

    PageHeader: [
        {
            Type: "Label",
            Name: "lblInvoiceNumber",
            Caption: "رقم الفاتورة",
            FontBold: True
        },
        {
            Type: "Label",
            Name: "lblDate",
            Caption: "التاريخ",
            FontBold: True
        },
        {
            Type: "Label",
            Name: "lblTotal",
            Caption: "الإجمالي",
            FontBold: True
        },
        {
            Type: "Label",
            Name: "lblPaid",
            Caption: "المدفوع",
            FontBold: True
        },
        {
            Type: "Label",
            Name: "lblRemaining",
            Caption: "المتبقي",
            FontBold: True
        }
    ],

    Detail: [
        {
            Type: "TextBox",
            Name: "txtInvoiceNumber",
            ControlSource: "رقم_الفاتورة"
        },
        {
            Type: "TextBox",
            Name: "txtDate",
            ControlSource: "التاريخ",
            Format: "dd/mm/yyyy"
        },
        {
            Type: "TextBox",
            Name: "txtTotal",
            ControlSource: "الإجمالي_النهائي",
            Format: "Currency"
        },
        {
            Type: "TextBox",
            Name: "txtPaid",
            ControlSource: "المدفوع",
            Format: "Currency"
        },
        {
            Type: "TextBox",
            Name: "txtRemaining",
            ControlSource: "المتبقي",
            Format: "Currency"
        }
    ],

    ReportFooter: [
        {
            Type: "TextBox",
            Name: "txtTotalBalance",
            ControlSource: "=Sum([المتبقي])",
            Caption: "إجمالي الرصيد:",
            Format: "Currency",
            FontBold: True
        }
    ]
}
