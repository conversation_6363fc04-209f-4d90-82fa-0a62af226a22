<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير المشتريات - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #8e44ad;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #7d3c98;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #8e44ad;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-box {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            max-width: 300px;
        }

        .purchases-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #8e44ad;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
            font-size: 0.9em;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-paid {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .status-partial {
            background: #f39c12;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .status-pending {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .action-btns {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .purchase-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-row, .form-row-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 فواتير المشتريات</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number" id="totalPurchases">2</div>
                <div class="stat-label">إجمالي فواتير المشتريات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPurchaseAmount">18,500</div>
                <div class="stat-label">إجمالي المشتريات (ج.م)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="paidPurchases">15,800</div>
                <div class="stat-label">المبلغ المدفوع (ج.م)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingPurchases">2,700</div>
                <div class="stat-label">المبلغ المستحق (ج.م)</div>
            </div>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="showPurchaseForm()">
                    ➕ فاتورة مشتريات جديدة
                </button>
                <button class="btn btn-primary" onclick="exportPurchases()">
                    📊 تصدير الفواتير
                </button>
                <button class="btn btn-warning" onclick="showPendingPurchases()">
                    ⏰ المستحقات للموردين
                </button>
                <button class="btn btn-info" onclick="generatePurchaseReport()">
                    📋 تقرير المشتريات
                </button>
                <input type="text" class="search-box" placeholder="🔍 البحث في فواتير المشتريات..." onkeyup="searchPurchases(this.value)">
            </div>
        </div>

        <!-- نموذج إنشاء فاتورة مشتريات جديدة -->
        <div id="purchaseForm" class="purchase-form">
            <h2>📝 إنشاء فاتورة مشتريات جديدة</h2>
            <form id="newPurchaseForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchaseNumber">رقم الفاتورة:</label>
                        <input type="text" id="purchaseNumber" readonly>
                    </div>
                    <div class="form-group">
                        <label for="purchaseDate">التاريخ:</label>
                        <input type="date" id="purchaseDate" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierSelect">المورد:</label>
                        <select id="supplierSelect" required>
                            <option value="">اختر المورد</option>
                            <option value="S0001">مطبعة الفجر</option>
                            <option value="S0002">شركة الورق المصرية</option>
                            <option value="S0003">مصنع الأحبار الحديثة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="paymentMethod">طريقة الدفع:</label>
                        <select id="paymentMethod">
                            <option value="نقدي">نقدي</option>
                            <option value="شيك">شيك</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="آجل">آجل</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="purchaseDescription">وصف المشتريات:</label>
                    <textarea id="purchaseDescription" rows="3" placeholder="اكتب تفاصيل المشتريات..."></textarea>
                </div>

                <div class="form-row-3">
                    <div class="form-group">
                        <label for="subtotal">الإجمالي قبل الضريبة:</label>
                        <input type="number" id="subtotal" step="0.01" min="0" onchange="calculateTotal()">
                    </div>
                    <div class="form-group">
                        <label for="tax">الضريبة (14%):</label>
                        <input type="number" id="tax" step="0.01" readonly>
                    </div>
                    <div class="form-group">
                        <label for="total">الإجمالي النهائي:</label>
                        <input type="number" id="total" step="0.01" readonly>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="paidAmount">المبلغ المدفوع:</label>
                        <input type="number" id="paidAmount" step="0.01" min="0" onchange="calculateRemaining()">
                    </div>
                    <div class="form-group">
                        <label for="remainingAmount">المبلغ المتبقي:</label>
                        <input type="number" id="remainingAmount" step="0.01" readonly>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات:</label>
                    <textarea id="notes" rows="2" placeholder="ملاحظات إضافية..."></textarea>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ الفاتورة</button>
                    <button type="button" class="btn btn-danger" onclick="hidePurchaseForm()">❌ إلغاء</button>
                </div>
            </form>
        </div>

        <div class="purchases-table">
            <div class="table-header">
                📋 قائمة فواتير المشتريات
            </div>
            <table id="purchasesTable">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>المورد</th>
                        <th>الوصف</th>
                        <th>الإجمالي</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="purchasesTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // بيانات فواتير المشتريات
        let purchases = [
            {
                number: 'PUR20240110001',
                date: '2024-01-10',
                supplierCode: 'S0001',
                supplierName: 'مطبعة الفجر',
                description: 'ورق طباعة A4 وأحبار ملونة',
                subtotal: 8000,
                tax: 1120,
                total: 9120,
                paid: 9120,
                remaining: 0,
                paymentMethod: 'نقدي',
                status: 'مدفوعة',
                notes: 'تم الاستلام بالكامل'
            },
            {
                number: 'PUR20240112001',
                date: '2024-01-12',
                supplierCode: 'S0002',
                supplierName: 'شركة الورق المصرية',
                description: 'ورق مقوى وكرتون للتغليف',
                subtotal: 6800,
                tax: 952,
                total: 7752,
                paid: 5052,
                remaining: 2700,
                paymentMethod: 'آجل',
                status: 'جزئية',
                notes: 'باقي المبلغ خلال 15 يوم'
            },
            {
                number: 'PUR20240115001',
                date: '2024-01-15',
                supplierCode: 'S0003',
                supplierName: 'مصنع الأحبار الحديثة',
                description: 'أحبار طباعة رقمية متنوعة',
                subtotal: 1400,
                tax: 196,
                total: 1596,
                paid: 1596,
                remaining: 0,
                paymentMethod: 'تحويل بنكي',
                status: 'مدفوعة',
                notes: 'جودة ممتازة'
            }
        ];

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadPurchases();
            updateStats();
            document.getElementById('purchaseDate').value = new Date().toISOString().split('T')[0];
        });

        function loadPurchases() {
            const tbody = document.getElementById('purchasesTableBody');
            tbody.innerHTML = '';

            purchases.forEach((purchase, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${purchase.number}</td>
                    <td>${purchase.date}</td>
                    <td>${purchase.supplierName}</td>
                    <td>${purchase.description}</td>
                    <td>${purchase.total.toLocaleString()} ج.م</td>
                    <td>${purchase.paid.toLocaleString()} ج.م</td>
                    <td style="color: ${purchase.remaining > 0 ? '#e74c3c' : '#27ae60'}">
                        ${purchase.remaining.toLocaleString()} ج.م
                    </td>
                    <td>${purchase.paymentMethod}</td>
                    <td>
                        <span class="status-${purchase.status === 'مدفوعة' ? 'paid' : purchase.status === 'جزئية' ? 'partial' : 'pending'}">
                            ${purchase.status}
                        </span>
                    </td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-primary" onclick="printPurchase(${index})">🖨️</button>
                            <button class="action-btn btn-warning" onclick="editPurchase(${index})">✏️</button>
                            <button class="action-btn btn-danger" onclick="deletePurchase(${index})">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateStats() {
            const totalPurchases = purchases.length;
            const totalPurchaseAmount = purchases.reduce((sum, pur) => sum + pur.total, 0);
            const paidPurchases = purchases.reduce((sum, pur) => sum + pur.paid, 0);
            const pendingPurchases = purchases.reduce((sum, pur) => sum + pur.remaining, 0);

            document.getElementById('totalPurchases').textContent = totalPurchases;
            document.getElementById('totalPurchaseAmount').textContent = totalPurchaseAmount.toLocaleString();
            document.getElementById('paidPurchases').textContent = paidPurchases.toLocaleString();
            document.getElementById('pendingPurchases').textContent = pendingPurchases.toLocaleString();
        }

        function showPurchaseForm() {
            document.getElementById('purchaseForm').style.display = 'block';
            document.getElementById('purchaseNumber').value = generatePurchaseNumber();
        }

        function hidePurchaseForm() {
            document.getElementById('purchaseForm').style.display = 'none';
            document.getElementById('newPurchaseForm').reset();
        }

        function generatePurchaseNumber() {
            const today = new Date();
            const dateStr = today.getFullYear().toString() + 
                           (today.getMonth() + 1).toString().padStart(2, '0') + 
                           today.getDate().toString().padStart(2, '0');
            const count = purchases.filter(pur => pur.number.includes(dateStr)).length + 1;
            return 'PUR' + dateStr + count.toString().padStart(3, '0');
        }

        function calculateTotal() {
            const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
            const tax = subtotal * 0.14;
            const total = subtotal + tax;
            
            document.getElementById('tax').value = tax.toFixed(2);
            document.getElementById('total').value = total.toFixed(2);
            document.getElementById('paidAmount').value = total.toFixed(2);
            calculateRemaining();
        }

        function calculateRemaining() {
            const total = parseFloat(document.getElementById('total').value) || 0;
            const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = total - paid;
            
            document.getElementById('remainingAmount').value = remaining.toFixed(2);
        }

        // حفظ فاتورة المشتريات الجديدة
        document.getElementById('newPurchaseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const supplierSelect = document.getElementById('supplierSelect');
            const supplierName = supplierSelect.options[supplierSelect.selectedIndex].text;
            
            const purchaseData = {
                number: document.getElementById('purchaseNumber').value,
                date: document.getElementById('purchaseDate').value,
                supplierCode: document.getElementById('supplierSelect').value,
                supplierName: supplierName === 'اختر المورد' ? '' : supplierName,
                description: document.getElementById('purchaseDescription').value,
                subtotal: parseFloat(document.getElementById('subtotal').value) || 0,
                tax: parseFloat(document.getElementById('tax').value) || 0,
                total: parseFloat(document.getElementById('total').value) || 0,
                paid: parseFloat(document.getElementById('paidAmount').value) || 0,
                remaining: parseFloat(document.getElementById('remainingAmount').value) || 0,
                paymentMethod: document.getElementById('paymentMethod').value,
                status: parseFloat(document.getElementById('remainingAmount').value) === 0 ? 'مدفوعة' : 'جزئية',
                notes: document.getElementById('notes').value
            };

            purchases.push(purchaseData);
            loadPurchases();
            updateStats();
            hidePurchaseForm();
            alert('تم إنشاء فاتورة المشتريات بنجاح!');
        });

        function searchPurchases(searchTerm) {
            const tbody = document.getElementById('purchasesTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function printPurchase(index) {
            const purchase = purchases[index];
            alert(`طباعة فاتورة المشتريات: ${purchase.number}\n\nالمورد: ${purchase.supplierName}\nالإجمالي: ${purchase.total.toLocaleString()} ج.م\nالوصف: ${purchase.description}\n\nسيتم فتح نافذة الطباعة...`);
        }

        function editPurchase(index) {
            alert('ميزة تعديل فاتورة المشتريات ستكون متاحة قريباً...');
        }

        function deletePurchase(index) {
            if (confirm('هل أنت متأكد من حذف فاتورة المشتريات هذه؟')) {
                purchases.splice(index, 1);
                loadPurchases();
                updateStats();
                alert('تم حذف فاتورة المشتريات بنجاح');
            }
        }

        function exportPurchases() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "رقم الفاتورة,التاريخ,كود المورد,اسم المورد,الوصف,الإجمالي قبل الضريبة,الضريبة,الإجمالي النهائي,المدفوع,المتبقي,طريقة الدفع,الحالة,ملاحظات\n"
                + purchases.map(pur => `${pur.number},${pur.date},${pur.supplierCode},${pur.supplierName},${pur.description},${pur.subtotal},${pur.tax},${pur.total},${pur.paid},${pur.remaining},${pur.paymentMethod},${pur.status},${pur.notes}`).join("\n");
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "فواتير_المشتريات_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function showPendingPurchases() {
            const pendingPurchases = purchases.filter(pur => pur.remaining > 0);
            
            if (pendingPurchases.length === 0) {
                alert('🎉 ممتاز! جميع فواتير المشتريات مدفوعة بالكامل');
                return;
            }
            
            let message = '⏰ المستحقات للموردين:\n\n';
            pendingPurchases.forEach(pur => {
                message += `• ${pur.number} - ${pur.supplierName}\n  المبلغ المستحق: ${pur.remaining.toLocaleString()} ج.م\n\n`;
            });
            
            alert(message);
        }

        function generatePurchaseReport() {
            const totalAmount = purchases.reduce((sum, pur) => sum + pur.total, 0);
            const paidAmount = purchases.reduce((sum, pur) => sum + pur.paid, 0);
            const pendingAmount = purchases.reduce((sum, pur) => sum + pur.remaining, 0);
            
            alert(`📊 تقرير المشتريات:\n\n` +
                  `إجمالي فواتير المشتريات: ${purchases.length}\n` +
                  `إجمالي قيمة المشتريات: ${totalAmount.toLocaleString()} ج.م\n` +
                  `المبلغ المدفوع: ${paidAmount.toLocaleString()} ج.م\n` +
                  `المبلغ المستحق: ${pendingAmount.toLocaleString()} ج.م\n` +
                  `نسبة السداد: ${((paidAmount/totalAmount)*100).toFixed(1)}%\n\n` +
                  'سيتم إنشاء تقرير مفصل قريباً...');
        }
    </script>
</body>
</html>
