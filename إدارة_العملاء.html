<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #2980b9;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-box {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            max-width: 300px;
        }

        .customers-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #34495e;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-active {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .status-inactive {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .action-btns {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 إدارة العملاء</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="openAddCustomerModal()">
                    ➕ إضافة عميل جديد
                </button>
                <button class="btn btn-primary" onclick="exportCustomers()">
                    📊 تصدير البيانات
                </button>
                <button class="btn btn-warning" onclick="importCustomers()">
                    📥 استيراد البيانات
                </button>
                <input type="text" class="search-box" placeholder="🔍 البحث عن عميل..." onkeyup="searchCustomers(this.value)">
            </div>
        </div>

        <div class="customers-table">
            <div class="table-header">
                📋 قائمة العملاء
            </div>
            <table id="customersTable">
                <thead>
                    <tr>
                        <th>كود العميل</th>
                        <th>اسم العميل</th>
                        <th>التليفون</th>
                        <th>الموبايل</th>
                        <th>العنوان</th>
                        <th>حد الائتمان</th>
                        <th>الرصيد الحالي</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="customersTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل عميل -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة عميل جديد</h2>
                <span class="close" onclick="closeCustomerModal()">&times;</span>
            </div>
            <form id="customerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerCode">كود العميل:</label>
                        <input type="text" id="customerCode" readonly>
                    </div>
                    <div class="form-group">
                        <label for="customerName">اسم العميل: *</label>
                        <input type="text" id="customerName" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="customerAddress">العنوان:</label>
                    <textarea id="customerAddress" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerPhone">التليفون:</label>
                        <input type="tel" id="customerPhone">
                    </div>
                    <div class="form-group">
                        <label for="customerMobile">الموبايل:</label>
                        <input type="tel" id="customerMobile">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerEmail">البريد الإلكتروني:</label>
                        <input type="email" id="customerEmail">
                    </div>
                    <div class="form-group">
                        <label for="customerTaxNumber">الرقم الضريبي:</label>
                        <input type="text" id="customerTaxNumber">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="creditLimit">حد الائتمان (ج.م):</label>
                        <input type="number" id="creditLimit" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="customerStatus">الحالة:</label>
                        <select id="customerStatus">
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="customerNotes">ملاحظات:</label>
                    <textarea id="customerNotes" rows="3"></textarea>
                </div>
                
                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ</button>
                    <button type="button" class="btn btn-danger" onclick="closeCustomerModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // بيانات العملاء (محفوظة محلياً)
        let customers = [
            {
                code: 'C0001',
                name: 'شركة الأهرام للتجارة',
                address: 'القاهرة - مصر الجديدة',
                phone: '0223456789',
                mobile: '01012345678',
                email: '<EMAIL>',
                taxNumber: '*********',
                creditLimit: 50000,
                currentBalance: 2700,
                status: 'نشط',
                notes: 'عميل مميز',
                registrationDate: '2024-01-15'
            },
            {
                code: 'C0002',
                name: 'مؤسسة النيل للخدمات',
                address: 'الجيزة - المهندسين',
                phone: '0233456789',
                mobile: '01*********',
                email: '<EMAIL>',
                taxNumber: '*********',
                creditLimit: 30000,
                currentBalance: 0,
                status: 'نشط',
                notes: 'عميل جديد',
                registrationDate: '2024-01-20'
            },
            {
                code: 'C0003',
                name: 'شركة الدلتا للصناعات',
                address: 'الإسكندرية - سموحة',
                phone: '0334567890',
                mobile: '0*********0',
                email: '<EMAIL>',
                taxNumber: '*********',
                creditLimit: 75000,
                currentBalance: 0,
                status: 'نشط',
                notes: 'عميل كبير',
                registrationDate: '2024-01-25'
            }
        ];

        let editingCustomerIndex = -1;

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadCustomers();
        });

        function loadCustomers() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';

            customers.forEach((customer, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.code}</td>
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td>${customer.mobile}</td>
                    <td>${customer.address}</td>
                    <td>${customer.creditLimit.toLocaleString()} ج.م</td>
                    <td style="color: ${customer.currentBalance > 0 ? '#e74c3c' : '#27ae60'}">
                        ${customer.currentBalance.toLocaleString()} ج.م
                    </td>
                    <td>
                        <span class="status-${customer.status === 'نشط' ? 'active' : 'inactive'}">
                            ${customer.status}
                        </span>
                    </td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-warning" onclick="editCustomer(${index})">✏️ تعديل</button>
                            <button class="action-btn btn-primary" onclick="viewCustomerStatement(${index})">📊 كشف حساب</button>
                            <button class="action-btn btn-danger" onclick="deleteCustomer(${index})">🗑️ حذف</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function openAddCustomerModal() {
            document.getElementById('modalTitle').textContent = 'إضافة عميل جديد';
            document.getElementById('customerCode').value = generateCustomerCode();
            document.getElementById('customerForm').reset();
            document.getElementById('customerCode').value = generateCustomerCode();
            editingCustomerIndex = -1;
            document.getElementById('customerModal').style.display = 'block';
        }

        function editCustomer(index) {
            const customer = customers[index];
            document.getElementById('modalTitle').textContent = 'تعديل بيانات العميل';
            
            document.getElementById('customerCode').value = customer.code;
            document.getElementById('customerName').value = customer.name;
            document.getElementById('customerAddress').value = customer.address;
            document.getElementById('customerPhone').value = customer.phone;
            document.getElementById('customerMobile').value = customer.mobile;
            document.getElementById('customerEmail').value = customer.email;
            document.getElementById('customerTaxNumber').value = customer.taxNumber;
            document.getElementById('creditLimit').value = customer.creditLimit;
            document.getElementById('customerStatus').value = customer.status;
            document.getElementById('customerNotes').value = customer.notes;
            
            editingCustomerIndex = index;
            document.getElementById('customerModal').style.display = 'block';
        }

        function closeCustomerModal() {
            document.getElementById('customerModal').style.display = 'none';
            document.getElementById('customerForm').reset();
            editingCustomerIndex = -1;
        }

        function generateCustomerCode() {
            const lastCode = customers.length > 0 ? 
                Math.max(...customers.map(c => parseInt(c.code.substring(1)))) : 0;
            return 'C' + String(lastCode + 1).padStart(4, '0');
        }

        // حفظ بيانات العميل
        document.getElementById('customerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const customerData = {
                code: document.getElementById('customerCode').value,
                name: document.getElementById('customerName').value,
                address: document.getElementById('customerAddress').value,
                phone: document.getElementById('customerPhone').value,
                mobile: document.getElementById('customerMobile').value,
                email: document.getElementById('customerEmail').value,
                taxNumber: document.getElementById('customerTaxNumber').value,
                creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
                currentBalance: editingCustomerIndex >= 0 ? customers[editingCustomerIndex].currentBalance : 0,
                status: document.getElementById('customerStatus').value,
                notes: document.getElementById('customerNotes').value,
                registrationDate: editingCustomerIndex >= 0 ? customers[editingCustomerIndex].registrationDate : new Date().toISOString().split('T')[0]
            };

            if (editingCustomerIndex >= 0) {
                customers[editingCustomerIndex] = customerData;
                alert('تم تحديث بيانات العميل بنجاح');
            } else {
                customers.push(customerData);
                alert('تم إضافة العميل بنجاح');
            }

            loadCustomers();
            closeCustomerModal();
        });

        function deleteCustomer(index) {
            if (confirm('هل أنت متأكد من حذف هذا العميل؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
                customers.splice(index, 1);
                loadCustomers();
                alert('تم حذف العميل بنجاح');
            }
        }

        function searchCustomers(searchTerm) {
            const tbody = document.getElementById('customersTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function viewCustomerStatement(index) {
            const customer = customers[index];
            alert(`كشف حساب العميل: ${customer.name}\n\nالرصيد الحالي: ${customer.currentBalance.toLocaleString()} ج.م\nحد الائتمان: ${customer.creditLimit.toLocaleString()} ج.م\n\nسيتم فتح التقرير المفصل قريباً...`);
        }

        function exportCustomers() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "كود العميل,اسم العميل,العنوان,التليفون,الموبايل,البريد الإلكتروني,الرقم الضريبي,حد الائتمان,الرصيد الحالي,الحالة,ملاحظات\n"
                + customers.map(c => `${c.code},${c.name},${c.address},${c.phone},${c.mobile},${c.email},${c.taxNumber},${c.creditLimit},${c.currentBalance},${c.status},${c.notes}`).join("\n");
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "عملاء_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function importCustomers() {
            alert('ميزة استيراد البيانات ستكون متاحة قريباً...');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('customerModal');
            if (event.target == modal) {
                closeCustomerModal();
            }
        }
    </script>
</body>
</html>
