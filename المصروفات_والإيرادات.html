<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المصروفات والإيرادات - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #e67e22;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #d35400;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .summary-card.revenue::before { background: #27ae60; }
        .summary-card.expense::before { background: #e74c3c; }
        .summary-card.profit::before { background: #3498db; }

        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .summary-card.revenue .summary-number { color: #27ae60; }
        .summary-card.expense .summary-number { color: #e74c3c; }
        .summary-card.profit .summary-number { color: #3498db; }

        .summary-label {
            color: #7f8c8d;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .summary-change {
            font-size: 0.9em;
            padding: 4px 12px;
            border-radius: 20px;
        }

        .change-positive {
            background: #d5f4e6;
            color: #27ae60;
        }

        .change-negative {
            background: #fab1a0;
            color: #e17055;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-primary { background: #3498db; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box, .filter-select {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .filter-select {
            min-width: 150px;
        }

        .transactions-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #e67e22;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
            font-size: 0.9em;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .type-revenue {
            background: #d5f4e6;
            color: #27ae60;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .type-expense {
            background: #fab1a0;
            color: #e17055;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .action-btns {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .transaction-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-row, .form-row-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group, .filter-row {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💸 المصروفات والإيرادات</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="summary-cards">
            <div class="summary-card revenue">
                <div class="summary-number" id="totalRevenue">45,200</div>
                <div class="summary-label">إجمالي الإيرادات (ج.م)</div>
                <div class="summary-change change-positive">+12% من الشهر الماضي</div>
            </div>
            <div class="summary-card expense">
                <div class="summary-number" id="totalExpenses">22,000</div>
                <div class="summary-label">إجمالي المصروفات (ج.م)</div>
                <div class="summary-change change-negative">+8% من الشهر الماضي</div>
            </div>
            <div class="summary-card profit">
                <div class="summary-number" id="netProfit">23,200</div>
                <div class="summary-label">صافي الربح (ج.م)</div>
                <div class="summary-change change-positive">+15% من الشهر الماضي</div>
            </div>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="showRevenueForm()">
                    ➕ إضافة إيراد
                </button>
                <button class="btn btn-danger" onclick="showExpenseForm()">
                    ➖ إضافة مصروف
                </button>
                <button class="btn btn-primary" onclick="exportTransactions()">
                    📊 تصدير البيانات
                </button>
                <button class="btn btn-info" onclick="generateFinancialReport()">
                    📋 التقرير المالي
                </button>
                <button class="btn btn-warning" onclick="showMonthlyComparison()">
                    📈 مقارنة شهرية
                </button>
            </div>
            
            <div class="filter-row">
                <input type="text" class="search-box" placeholder="🔍 البحث في المعاملات..." onkeyup="searchTransactions(this.value)">
                <select class="filter-select" onchange="filterByType(this.value)">
                    <option value="">جميع المعاملات</option>
                    <option value="إيراد">الإيرادات فقط</option>
                    <option value="مصروف">المصروفات فقط</option>
                </select>
                <select class="filter-select" onchange="filterByCategory(this.value)">
                    <option value="">جميع الفئات</option>
                    <option value="مبيعات">مبيعات</option>
                    <option value="إيجار">إيجار</option>
                    <option value="رواتب">رواتب</option>
                    <option value="مرافق">مرافق</option>
                    <option value="تسويق">تسويق</option>
                    <option value="أخرى">أخرى</option>
                </select>
                <input type="month" class="filter-select" onchange="filterByMonth(this.value)">
            </div>
        </div>

        <!-- نموذج إضافة معاملة -->
        <div id="transactionForm" class="transaction-form">
            <h2 id="formTitle">إضافة معاملة جديدة</h2>
            <form id="newTransactionForm">
                <input type="hidden" id="transactionType" value="">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="transactionDate">التاريخ:</label>
                        <input type="date" id="transactionDate" required>
                    </div>
                    <div class="form-group">
                        <label for="transactionCategory">الفئة:</label>
                        <select id="transactionCategory" required>
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="transactionAmount">المبلغ (ج.م):</label>
                        <input type="number" id="transactionAmount" step="0.01" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="paymentMethod">طريقة الدفع/الاستلام:</label>
                        <select id="paymentMethod">
                            <option value="نقدي">نقدي</option>
                            <option value="شيك">شيك</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="transactionDescription">الوصف:</label>
                    <textarea id="transactionDescription" rows="3" placeholder="اكتب وصف المعاملة..." required></textarea>
                </div>

                <div class="form-group">
                    <label for="transactionNotes">ملاحظات:</label>
                    <textarea id="transactionNotes" rows="2" placeholder="ملاحظات إضافية..."></textarea>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ المعاملة</button>
                    <button type="button" class="btn btn-danger" onclick="hideTransactionForm()">❌ إلغاء</button>
                </div>
            </form>
        </div>

        <div class="transactions-table">
            <div class="table-header">
                📋 سجل المعاملات المالية
            </div>
            <table id="transactionsTable">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>النوع</th>
                        <th>الفئة</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>ملاحظات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="transactionsTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // بيانات المعاملات المالية
        let transactions = [
            {
                id: 1,
                date: '2024-01-15',
                type: 'إيراد',
                category: 'مبيعات',
                description: 'فاتورة مبيعات - شركة الأهرام',
                amount: 11400,
                paymentMethod: 'نقدي',
                notes: 'دفع فوري'
            },
            {
                id: 2,
                date: '2024-01-15',
                type: 'مصروف',
                category: 'إيجار',
                description: 'إيجار المكتب شهر يناير',
                amount: 5000,
                paymentMethod: 'نقدي',
                notes: 'مصروف شهري'
            },
            {
                id: 3,
                date: '2024-01-16',
                type: 'إيراد',
                category: 'مبيعات',
                description: 'فاتورة مبيعات - مؤسسة النيل',
                amount: 5700,
                paymentMethod: 'آجل',
                notes: 'دفعة جزئية'
            },
            {
                id: 4,
                date: '2024-01-16',
                type: 'مصروف',
                category: 'رواتب',
                description: 'رواتب الموظفين',
                amount: 15000,
                paymentMethod: 'تحويل بنكي',
                notes: 'رواتب شهرية'
            },
            {
                id: 5,
                date: '2024-01-17',
                type: 'إيراد',
                category: 'مبيعات',
                description: 'فاتورة مبيعات - شركة الدلتا',
                amount: 17100,
                paymentMethod: 'تحويل بنكي',
                notes: 'عميل كبير'
            },
            {
                id: 6,
                date: '2024-01-17',
                type: 'مصروف',
                category: 'مرافق',
                description: 'فاتورة الكهرباء',
                amount: 800,
                paymentMethod: 'نقدي',
                notes: 'مرافق شهرية'
            },
            {
                id: 7,
                date: '2024-01-18',
                type: 'مصروف',
                category: 'تسويق',
                description: 'إعلانات على وسائل التواصل',
                amount: 1200,
                paymentMethod: 'بطاقة ائتمان',
                notes: 'حملة تسويقية'
            },
            {
                id: 8,
                date: '2024-01-20',
                type: 'إيراد',
                category: 'أخرى',
                description: 'عمولة من شريك تجاري',
                amount: 2000,
                paymentMethod: 'تحويل بنكي',
                notes: 'عمولة ربع سنوية'
            }
        ];

        let nextTransactionId = 9;

        // فئات الإيرادات والمصروفات
        const revenueCategories = ['مبيعات', 'خدمات', 'عمولات', 'استثمارات', 'أخرى'];
        const expenseCategories = ['إيجار', 'رواتب', 'مرافق', 'تسويق', 'مواد خام', 'صيانة', 'مواصلات', 'أخرى'];

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadTransactions();
            updateSummary();
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];
        });

        function loadTransactions() {
            const tbody = document.getElementById('transactionsTableBody');
            tbody.innerHTML = '';

            // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
            const sortedTransactions = [...transactions].sort((a, b) => new Date(b.date) - new Date(a.date));

            sortedTransactions.forEach((transaction, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${transaction.date}</td>
                    <td>
                        <span class="type-${transaction.type === 'إيراد' ? 'revenue' : 'expense'}">
                            ${transaction.type}
                        </span>
                    </td>
                    <td>${transaction.category}</td>
                    <td>${transaction.description}</td>
                    <td style="color: ${transaction.type === 'إيراد' ? '#27ae60' : '#e74c3c'}; font-weight: bold;">
                        ${transaction.type === 'إيراد' ? '+' : '-'}${transaction.amount.toLocaleString()} ج.م
                    </td>
                    <td>${transaction.paymentMethod}</td>
                    <td>${transaction.notes}</td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-warning" onclick="editTransaction(${transaction.id})">✏️</button>
                            <button class="action-btn btn-danger" onclick="deleteTransaction(${transaction.id})">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateSummary() {
            const revenues = transactions.filter(t => t.type === 'إيراد');
            const expenses = transactions.filter(t => t.type === 'مصروف');
            
            const totalRevenue = revenues.reduce((sum, t) => sum + t.amount, 0);
            const totalExpenses = expenses.reduce((sum, t) => sum + t.amount, 0);
            const netProfit = totalRevenue - totalExpenses;

            document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString();
            document.getElementById('totalExpenses').textContent = totalExpenses.toLocaleString();
            document.getElementById('netProfit').textContent = netProfit.toLocaleString();
        }

        function showRevenueForm() {
            document.getElementById('formTitle').textContent = '💰 إضافة إيراد جديد';
            document.getElementById('transactionType').value = 'إيراد';
            populateCategories(revenueCategories);
            document.getElementById('transactionForm').style.display = 'block';
        }

        function showExpenseForm() {
            document.getElementById('formTitle').textContent = '💸 إضافة مصروف جديد';
            document.getElementById('transactionType').value = 'مصروف';
            populateCategories(expenseCategories);
            document.getElementById('transactionForm').style.display = 'block';
        }

        function populateCategories(categories) {
            const categorySelect = document.getElementById('transactionCategory');
            categorySelect.innerHTML = '<option value="">اختر الفئة</option>';
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }

        function hideTransactionForm() {
            document.getElementById('transactionForm').style.display = 'none';
            document.getElementById('newTransactionForm').reset();
        }

        // حفظ المعاملة الجديدة
        document.getElementById('newTransactionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const transactionData = {
                id: nextTransactionId++,
                date: document.getElementById('transactionDate').value,
                type: document.getElementById('transactionType').value,
                category: document.getElementById('transactionCategory').value,
                description: document.getElementById('transactionDescription').value,
                amount: parseFloat(document.getElementById('transactionAmount').value),
                paymentMethod: document.getElementById('paymentMethod').value,
                notes: document.getElementById('transactionNotes').value
            };

            transactions.push(transactionData);
            loadTransactions();
            updateSummary();
            hideTransactionForm();
            alert(`تم إضافة ${transactionData.type} بنجاح!`);
        });

        function searchTransactions(searchTerm) {
            const tbody = document.getElementById('transactionsTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterByType(type) {
            const tbody = document.getElementById('transactionsTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                if (type === '' || row.cells[1].textContent.includes(type)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterByCategory(category) {
            const tbody = document.getElementById('transactionsTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                if (category === '' || row.cells[2].textContent === category) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterByMonth(month) {
            if (!month) {
                loadTransactions();
                return;
            }
            
            const tbody = document.getElementById('transactionsTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const rowDate = row.cells[0].textContent;
                if (rowDate.startsWith(month)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function editTransaction(id) {
            alert('ميزة تعديل المعاملة ستكون متاحة قريباً...');
        }

        function deleteTransaction(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                transactions = transactions.filter(t => t.id !== id);
                loadTransactions();
                updateSummary();
                alert('تم حذف المعاملة بنجاح');
            }
        }

        function exportTransactions() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "التاريخ,النوع,الفئة,الوصف,المبلغ,طريقة الدفع,ملاحظات\n"
                + transactions.map(t => `${t.date},${t.type},${t.category},${t.description},${t.amount},${t.paymentMethod},${t.notes}`).join("\n");
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "المعاملات_المالية_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateFinancialReport() {
            const revenues = transactions.filter(t => t.type === 'إيراد');
            const expenses = transactions.filter(t => t.type === 'مصروف');
            
            const totalRevenue = revenues.reduce((sum, t) => sum + t.amount, 0);
            const totalExpenses = expenses.reduce((sum, t) => sum + t.amount, 0);
            const netProfit = totalRevenue - totalExpenses;
            const profitMargin = totalRevenue > 0 ? ((netProfit / totalRevenue) * 100).toFixed(1) : 0;
            
            alert(`📊 التقرير المالي:\n\n` +
                  `إجمالي الإيرادات: ${totalRevenue.toLocaleString()} ج.م\n` +
                  `إجمالي المصروفات: ${totalExpenses.toLocaleString()} ج.م\n` +
                  `صافي الربح: ${netProfit.toLocaleString()} ج.م\n` +
                  `هامش الربح: ${profitMargin}%\n` +
                  `عدد المعاملات: ${transactions.length}\n\n` +
                  'سيتم إنشاء تقرير مفصل قريباً...');
        }

        function showMonthlyComparison() {
            alert('📈 المقارنة الشهرية:\n\nهذا الشهر مقارنة بالشهر الماضي:\n\n' +
                  '• الإيرادات: +12% ⬆️\n' +
                  '• المصروفات: +8% ⬆️\n' +
                  '• صافي الربح: +15% ⬆️\n\n' +
                  'الأداء ممتاز! استمر على هذا المنوال 🎉');
        }
    </script>
</body>
</html>
