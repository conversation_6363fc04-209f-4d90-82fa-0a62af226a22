<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأصناف - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #9b59b6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #8e44ad;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-filter-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            min-width: 250px;
        }

        .filter-select {
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            min-width: 150px;
        }

        .items-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #9b59b6;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
            font-size: 0.9em;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
            position: sticky;
            top: 0;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .category-badge {
            background: #3498db;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .category-badge.مطبوعات { background: #27ae60; }
        .category-badge.إعلانات { background: #e74c3c; }
        .category-badge.تصميم { background: #f39c12; }

        .stock-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .stock-good { background: #d5f4e6; color: #27ae60; }
        .stock-low { background: #ffeaa7; color: #d63031; }
        .stock-out { background: #fab1a0; color: #e17055; }

        .action-btns {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 3% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 700px;
            max-height: 85vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #ecf0f1;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #9b59b6;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-row, .form-row-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group, .search-filter-row {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            th, td {
                padding: 8px;
                font-size: 0.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 إدارة الأصناف والمخزون</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number" id="totalItems">5</div>
                <div class="stat-label">إجمالي الأصناف</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="lowStockItems">0</div>
                <div class="stat-label">أصناف تحت الحد الأدنى</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalStockValue">0</div>
                <div class="stat-label">قيمة المخزون (ج.م)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeCategories">3</div>
                <div class="stat-label">المجموعات النشطة</div>
            </div>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="openAddItemModal()">
                    ➕ إضافة صنف جديد
                </button>
                <button class="btn btn-warning" onclick="showLowStockItems()">
                    ⚠️ الأصناف الناقصة
                </button>
                <button class="btn btn-primary" onclick="exportItems()">
                    📊 تصدير البيانات
                </button>
                <button class="btn btn-info" onclick="generateStockReport()">
                    📋 تقرير المخزون
                </button>
            </div>
            
            <div class="search-filter-row">
                <input type="text" class="search-box" placeholder="🔍 البحث عن صنف..." onkeyup="searchItems(this.value)">
                <select class="filter-select" onchange="filterByCategory(this.value)">
                    <option value="">جميع المجموعات</option>
                    <option value="مطبوعات">مطبوعات</option>
                    <option value="إعلانات خارجية">إعلانات خارجية</option>
                    <option value="تصميم جرافيكي">تصميم جرافيكي</option>
                </select>
                <select class="filter-select" onchange="filterByStock(this.value)">
                    <option value="">جميع حالات المخزون</option>
                    <option value="متوفر">متوفر</option>
                    <option value="منخفض">منخفض</option>
                    <option value="نفد">نفد المخزون</option>
                </select>
            </div>
        </div>

        <div class="items-table">
            <div class="table-header">
                📋 قائمة الأصناف والمخزون
            </div>
            <table id="itemsTable">
                <thead>
                    <tr>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>المجموعة</th>
                        <th>الوحدة</th>
                        <th>سعر الشراء</th>
                        <th>سعر البيع</th>
                        <th>الكمية المتاحة</th>
                        <th>الحد الأدنى</th>
                        <th>حالة المخزون</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="itemsTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل صنف -->
    <div id="itemModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">إضافة صنف جديد</h2>
                <span class="close" onclick="closeItemModal()">&times;</span>
            </div>
            <form id="itemForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemCode">كود الصنف:</label>
                        <input type="text" id="itemCode" readonly>
                    </div>
                    <div class="form-group">
                        <label for="itemName">اسم الصنف: *</label>
                        <input type="text" id="itemName" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="itemDescription">الوصف:</label>
                    <textarea id="itemDescription" rows="3"></textarea>
                </div>
                
                <div class="form-row-3">
                    <div class="form-group">
                        <label for="itemUnit">الوحدة:</label>
                        <select id="itemUnit">
                            <option value="قطعة">قطعة</option>
                            <option value="متر">متر</option>
                            <option value="متر مربع">متر مربع</option>
                            <option value="كيلو">كيلو</option>
                            <option value="علبة">علبة</option>
                            <option value="حزمة">حزمة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="itemCategory">المجموعة:</label>
                        <select id="itemCategory">
                            <option value="مطبوعات">مطبوعات</option>
                            <option value="إعلانات خارجية">إعلانات خارجية</option>
                            <option value="تصميم جرافيكي">تصميم جرافيكي</option>
                            <option value="خامات">خامات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="itemStatus">الحالة:</label>
                        <select id="itemStatus">
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchasePrice">سعر الشراء (ج.م):</label>
                        <input type="number" id="purchasePrice" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="salePrice">سعر البيع (ج.م):</label>
                        <input type="number" id="salePrice" min="0" step="0.01">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="currentQuantity">الكمية المتاحة:</label>
                        <input type="number" id="currentQuantity" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="minQuantity">الحد الأدنى:</label>
                        <input type="number" id="minQuantity" min="0" step="0.01">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="itemNotes">ملاحظات:</label>
                    <textarea id="itemNotes" rows="3"></textarea>
                </div>
                
                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ</button>
                    <button type="button" class="btn btn-danger" onclick="closeItemModal()">❌ إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // بيانات الأصناف
        let items = [
            {
                code: 'I0001',
                name: 'بروشور A4',
                description: 'بروشور مطبوع على ورق A4 عالي الجودة',
                unit: 'قطعة',
                category: 'مطبوعات',
                purchasePrice: 2.50,
                salePrice: 5.00,
                currentQuantity: 1000,
                minQuantity: 100,
                status: 'نشط',
                notes: 'منتج أساسي',
                addedDate: '2024-01-15'
            },
            {
                code: 'I0002',
                name: 'فلاير A5',
                description: 'فلاير إعلاني مقاس A5',
                unit: 'قطعة',
                category: 'مطبوعات',
                purchasePrice: 1.50,
                salePrice: 3.00,
                currentQuantity: 2000,
                minQuantity: 200,
                status: 'نشط',
                notes: 'منتج شائع',
                addedDate: '2024-01-15'
            },
            {
                code: 'I0003',
                name: 'بانر إعلاني',
                description: 'بانر إعلاني للأحداث والمعارض',
                unit: 'متر مربع',
                category: 'إعلانات خارجية',
                purchasePrice: 25.00,
                salePrice: 50.00,
                currentQuantity: 50,
                minQuantity: 10,
                status: 'نشط',
                notes: 'منتج مميز',
                addedDate: '2024-01-16'
            },
            {
                code: 'I0004',
                name: 'كارت شخصي',
                description: 'كارت شخصي مطبوع بجودة عالية',
                unit: 'علبة 100 قطعة',
                category: 'مطبوعات',
                purchasePrice: 15.00,
                salePrice: 30.00,
                currentQuantity: 500,
                minQuantity: 50,
                status: 'نشط',
                notes: 'منتج أساسي',
                addedDate: '2024-01-17'
            },
            {
                code: 'I0005',
                name: 'ستيكر إعلاني',
                description: 'ستيكر إعلاني مقاوم للماء',
                unit: 'قطعة',
                category: 'مطبوعات',
                purchasePrice: 0.50,
                salePrice: 1.50,
                currentQuantity: 5000,
                minQuantity: 500,
                status: 'نشط',
                notes: 'منتج اقتصادي',
                addedDate: '2024-01-18'
            }
        ];

        let editingItemIndex = -1;

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadItems();
            updateStats();
        });

        function loadItems() {
            const tbody = document.getElementById('itemsTableBody');
            tbody.innerHTML = '';

            items.forEach((item, index) => {
                const stockStatus = getStockStatus(item);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.code}</td>
                    <td>${item.name}</td>
                    <td>
                        <span class="category-badge ${item.category}">
                            ${item.category}
                        </span>
                    </td>
                    <td>${item.unit}</td>
                    <td>${item.purchasePrice.toFixed(2)} ج.م</td>
                    <td>${item.salePrice.toFixed(2)} ج.م</td>
                    <td>${item.currentQuantity.toLocaleString()}</td>
                    <td>${item.minQuantity.toLocaleString()}</td>
                    <td>
                        <span class="stock-status ${stockStatus.class}">
                            ${stockStatus.text}
                        </span>
                    </td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-warning" onclick="editItem(${index})">✏️</button>
                            <button class="action-btn btn-info" onclick="adjustStock(${index})">📊</button>
                            <button class="action-btn btn-danger" onclick="deleteItem(${index})">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStockStatus(item) {
            if (item.currentQuantity <= 0) {
                return { class: 'stock-out', text: 'نفد المخزون' };
            } else if (item.currentQuantity <= item.minQuantity) {
                return { class: 'stock-low', text: 'منخفض' };
            } else {
                return { class: 'stock-good', text: 'متوفر' };
            }
        }

        function updateStats() {
            const totalItems = items.length;
            const lowStockItems = items.filter(item => item.currentQuantity <= item.minQuantity).length;
            const totalStockValue = items.reduce((sum, item) => sum + (item.currentQuantity * item.purchasePrice), 0);
            const activeCategories = [...new Set(items.map(item => item.category))].length;

            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('lowStockItems').textContent = lowStockItems;
            document.getElementById('totalStockValue').textContent = totalStockValue.toLocaleString();
            document.getElementById('activeCategories').textContent = activeCategories;
        }

        function openAddItemModal() {
            document.getElementById('modalTitle').textContent = 'إضافة صنف جديد';
            document.getElementById('itemCode').value = generateItemCode();
            document.getElementById('itemForm').reset();
            document.getElementById('itemCode').value = generateItemCode();
            editingItemIndex = -1;
            document.getElementById('itemModal').style.display = 'block';
        }

        function editItem(index) {
            const item = items[index];
            document.getElementById('modalTitle').textContent = 'تعديل بيانات الصنف';

            document.getElementById('itemCode').value = item.code;
            document.getElementById('itemName').value = item.name;
            document.getElementById('itemDescription').value = item.description;
            document.getElementById('itemUnit').value = item.unit;
            document.getElementById('itemCategory').value = item.category;
            document.getElementById('purchasePrice').value = item.purchasePrice;
            document.getElementById('salePrice').value = item.salePrice;
            document.getElementById('currentQuantity').value = item.currentQuantity;
            document.getElementById('minQuantity').value = item.minQuantity;
            document.getElementById('itemStatus').value = item.status;
            document.getElementById('itemNotes').value = item.notes;

            editingItemIndex = index;
            document.getElementById('itemModal').style.display = 'block';
        }

        function closeItemModal() {
            document.getElementById('itemModal').style.display = 'none';
            document.getElementById('itemForm').reset();
            editingItemIndex = -1;
        }

        function generateItemCode() {
            const lastCode = items.length > 0 ?
                Math.max(...items.map(i => parseInt(i.code.substring(1)))) : 0;
            return 'I' + String(lastCode + 1).padStart(4, '0');
        }

        // حفظ بيانات الصنف
        document.getElementById('itemForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const itemData = {
                code: document.getElementById('itemCode').value,
                name: document.getElementById('itemName').value,
                description: document.getElementById('itemDescription').value,
                unit: document.getElementById('itemUnit').value,
                category: document.getElementById('itemCategory').value,
                purchasePrice: parseFloat(document.getElementById('purchasePrice').value) || 0,
                salePrice: parseFloat(document.getElementById('salePrice').value) || 0,
                currentQuantity: parseFloat(document.getElementById('currentQuantity').value) || 0,
                minQuantity: parseFloat(document.getElementById('minQuantity').value) || 0,
                status: document.getElementById('itemStatus').value,
                notes: document.getElementById('itemNotes').value,
                addedDate: editingItemIndex >= 0 ? items[editingItemIndex].addedDate : new Date().toISOString().split('T')[0]
            };

            if (editingItemIndex >= 0) {
                items[editingItemIndex] = itemData;
                alert('تم تحديث بيانات الصنف بنجاح');
            } else {
                items.push(itemData);
                alert('تم إضافة الصنف بنجاح');
            }

            loadItems();
            updateStats();
            closeItemModal();
        });

        function deleteItem(index) {
            if (confirm('هل أنت متأكد من حذف هذا الصنف؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
                items.splice(index, 1);
                loadItems();
                updateStats();
                alert('تم حذف الصنف بنجاح');
            }
        }

        function adjustStock(index) {
            const item = items[index];
            const newQuantity = prompt(`تعديل كمية المخزون للصنف: ${item.name}\n\nالكمية الحالية: ${item.currentQuantity}\nأدخل الكمية الجديدة:`, item.currentQuantity);

            if (newQuantity !== null && !isNaN(newQuantity) && newQuantity >= 0) {
                items[index].currentQuantity = parseFloat(newQuantity);
                loadItems();
                updateStats();
                alert('تم تحديث كمية المخزون بنجاح');
            }
        }

        function searchItems(searchTerm) {
            const tbody = document.getElementById('itemsTableBody');
            const rows = tbody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterByCategory(category) {
            const tbody = document.getElementById('itemsTableBody');
            const rows = tbody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                if (category === '' || items[i].category === category) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function filterByStock(stockFilter) {
            const tbody = document.getElementById('itemsTableBody');
            const rows = tbody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const item = items[i];
                const row = rows[i];
                let showRow = false;

                if (stockFilter === '') {
                    showRow = true;
                } else if (stockFilter === 'متوفر' && item.currentQuantity > item.minQuantity) {
                    showRow = true;
                } else if (stockFilter === 'منخفض' && item.currentQuantity <= item.minQuantity && item.currentQuantity > 0) {
                    showRow = true;
                } else if (stockFilter === 'نفد' && item.currentQuantity <= 0) {
                    showRow = true;
                }

                row.style.display = showRow ? '' : 'none';
            }
        }

        function showLowStockItems() {
            const lowStockItems = items.filter(item => item.currentQuantity <= item.minQuantity);

            if (lowStockItems.length === 0) {
                alert('🎉 ممتاز! جميع الأصناف متوفرة بكميات كافية');
                return;
            }

            let message = '⚠️ الأصناف التي تحتاج إعادة تموين:\n\n';
            lowStockItems.forEach(item => {
                message += `• ${item.name} - الكمية الحالية: ${item.currentQuantity} ${item.unit}\n`;
            });

            alert(message);
        }

        function exportItems() {
            const csvContent = "data:text/csv;charset=utf-8,"
                + "كود الصنف,اسم الصنف,الوصف,الوحدة,المجموعة,سعر الشراء,سعر البيع,الكمية المتاحة,الحد الأدنى,الحالة,ملاحظات\n"
                + items.map(i => `${i.code},${i.name},${i.description},${i.unit},${i.category},${i.purchasePrice},${i.salePrice},${i.currentQuantity},${i.minQuantity},${i.status},${i.notes}`).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "أصناف_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateStockReport() {
            const totalValue = items.reduce((sum, item) => sum + (item.currentQuantity * item.purchasePrice), 0);
            const lowStockCount = items.filter(item => item.currentQuantity <= item.minQuantity).length;

            alert(`📊 تقرير المخزون:\n\n` +
                  `إجمالي الأصناف: ${items.length}\n` +
                  `قيمة المخزون الإجمالية: ${totalValue.toLocaleString()} ج.م\n` +
                  `الأصناف تحت الحد الأدنى: ${lowStockCount}\n` +
                  `المجموعات النشطة: ${[...new Set(items.map(item => item.category))].length}\n\n` +
                  'سيتم إنشاء تقرير مفصل قريباً...');
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('itemModal');
            if (event.target == modal) {
                closeItemModal();
            }
        }
    </script>
</body>
</html>
