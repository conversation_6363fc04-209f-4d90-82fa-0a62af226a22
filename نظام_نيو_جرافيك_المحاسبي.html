<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نيو جرافيك المحاسبي المتكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .company-name {
            color: #2c3e50;
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .login-section {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto 30px;
        }

        .login-section h2 {
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .dashboard {
            display: none;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .summary-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .summary-card h3 {
            color: white;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }

        .btn-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .nav-btn {
            padding: 20px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            color: white;
        }

        .nav-btn.customers { background: linear-gradient(135deg, #00b894, #00a085); }
        .nav-btn.suppliers { background: linear-gradient(135deg, #fdcb6e, #e17055); }
        .nav-btn.items { background: linear-gradient(135deg, #6c5ce7, #a29bfe); }
        .nav-btn.sales { background: linear-gradient(135deg, #fd79a8, #e84393); }
        .nav-btn.purchases { background: linear-gradient(135deg, #ff7675, #d63031); }
        .nav-btn.expenses { background: linear-gradient(135deg, #636e72, #2d3436); }
        .nav-btn.reports { background: linear-gradient(135deg, #00cec9, #00b894); }

        .nav-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .current-user {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            color: #2c3e50;
            font-weight: bold;
        }

        .logout-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .default-credentials {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .default-credentials h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .default-credentials p {
            color: #7f8c8d;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="company-name">شركة نيو جرافيك للدعاية والإعلان</h1>
            <p class="subtitle">نظام محاسبي متكامل لإدارة المطبوعات والإعلانات</p>
        </div>

        <div id="loginSection" class="login-section">
            <h2>تسجيل الدخول</h2>
            
            <div class="default-credentials">
                <h4>🔐 بيانات الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> 123456</p>
            </div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="login-btn">دخول</button>
            </form>
        </div>

        <div id="currentUser" class="current-user" style="display: none;">
            المستخدم: <span id="userDisplay"></span>
        </div>

        <button id="logoutBtn" class="logout-btn" style="display: none;" onclick="logout()">
            خروج
        </button>

        <div id="dashboard" class="dashboard">
            <div class="card summary-card">
                <h3>📊 الملخص المالي السريع</h3>
                <div class="summary-item">
                    <span>إجمالي المبيعات (الشهر الحالي):</span>
                    <span>34,200.00 ج.م</span>
                </div>
                <div class="summary-item">
                    <span>إجمالي المشتريات (الشهر الحالي):</span>
                    <span>18,500.00 ج.م</span>
                </div>
                <div class="summary-item">
                    <span>صافي الربح (الشهر الحالي):</span>
                    <span>15,700.00 ج.م</span>
                </div>
                <div class="summary-item">
                    <span>عدد العملاء النشطين:</span>
                    <span>3</span>
                </div>
                <div class="summary-item">
                    <span>عدد الموردين النشطين:</span>
                    <span>3</span>
                </div>
            </div>

            <div class="card">
                <h3>🎛️ الوحدات الرئيسية</h3>
                <div class="btn-grid">
                    <button class="nav-btn customers" onclick="openModule('customers')">
                        👥 إدارة العملاء
                    </button>
                    <button class="nav-btn suppliers" onclick="openModule('suppliers')">
                        🏭 إدارة الموردين
                    </button>
                    <button class="nav-btn items" onclick="openModule('items')">
                        📦 إدارة الأصناف
                    </button>
                    <button class="nav-btn sales" onclick="openModule('sales')">
                        🧾 فواتير المبيعات
                    </button>
                    <button class="nav-btn purchases" onclick="openModule('purchases')">
                        📋 فواتير المشتريات
                    </button>
                    <button class="nav-btn expenses" onclick="openModule('expenses')">
                        💸 المصروفات والإيرادات
                    </button>
                    <button class="nav-btn reports" onclick="openModule('reports')">
                        📈 التقارير المالية
                    </button>
                </div>
            </div>

            <div class="card">
                <h3>🔄 آخر العمليات</h3>
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 10px; border: 1px solid #dee2e6;">التاريخ</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6;">النوع</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6;">العميل/المورد</th>
                                <th style="padding: 10px; border: 1px solid #dee2e6;">المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">2024-01-17</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">فاتورة مبيعات</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">شركة الدلتا للصناعات</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">17,100.00 ج.م</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">2024-01-16</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">فاتورة مبيعات</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">مؤسسة النيل للخدمات</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">5,700.00 ج.م</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">2024-01-15</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">فاتورة مبيعات</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">شركة الأهرام للتجارة</td>
                                <td style="padding: 8px; border: 1px solid #dee2e6;">11,400.00 ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // التحقق من بيانات الدخول
            if (username === 'admin' && password === '123456') {
                login(username);
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        function login(username) {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('dashboard').style.display = 'grid';
            document.getElementById('currentUser').style.display = 'block';
            document.getElementById('logoutBtn').style.display = 'block';
            document.getElementById('userDisplay').textContent = username;
            
            alert('مرحباً ' + username + '! تم تسجيل الدخول بنجاح');
        }

        function logout() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('currentUser').style.display = 'none';
            document.getElementById('logoutBtn').style.display = 'none';
            
            // مسح الحقول
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            
            alert('تم تسجيل الخروج بنجاح');
        }

        function openModule(module) {
            const modulePages = {
                'customers': 'إدارة_العملاء.html',
                'suppliers': 'إدارة_الموردين.html',
                'items': 'إدارة_الأصناف.html',
                'sales': 'فواتير_المبيعات.html',
                'purchases': 'فواتير_المشتريات.html',
                'expenses': 'المصروفات_والإيرادات.html',
                'reports': 'التقارير_المالية.html'
            };

            if (modulePages[module]) {
                window.open(modulePages[module], '_blank');
            } else {
                alert('هذه الوحدة قيد التطوير...\nسيتم إضافتها قريباً!');
            }
        }

        // ملء الحقول تلقائياً للتجربة
        document.getElementById('username').value = 'admin';
        document.getElementById('password').value = '123456';
    </script>
</body>
</html>
