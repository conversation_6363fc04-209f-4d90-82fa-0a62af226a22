<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فواتير المبيعات - نظام نيو جرافيك</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            min-height: 100vh;
            direction: rtl;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }

        .back-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #c0392b;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .search-box {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
            max-width: 300px;
        }

        .invoices-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #e74c3c;
            color: white;
            padding: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
            font-size: 0.9em;
        }

        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .status-paid {
            background: #27ae60;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .status-partial {
            background: #f39c12;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .status-pending {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
        }

        .action-btns {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
        }

        .invoice-form {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #34495e;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        @media (max-width: 768px) {
            .form-row, .form-row-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧾 فواتير المبيعات</h1>
            <a href="نظام_نيو_جرافيك_المحاسبي.html" class="back-btn">🏠 العودة للرئيسية</a>
        </div>

        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-number" id="totalInvoices">3</div>
                <div class="stat-label">إجمالي الفواتير</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSales">34,200</div>
                <div class="stat-label">إجمالي المبيعات (ج.م)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="paidAmount">31,500</div>
                <div class="stat-label">المبلغ المحصل (ج.م)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingAmount">2,700</div>
                <div class="stat-label">المبلغ المستحق (ج.م)</div>
            </div>
        </div>

        <div class="controls">
            <div class="btn-group">
                <button class="btn btn-success" onclick="showInvoiceForm()">
                    ➕ فاتورة جديدة
                </button>
                <button class="btn btn-primary" onclick="exportInvoices()">
                    📊 تصدير الفواتير
                </button>
                <button class="btn btn-warning" onclick="showPendingInvoices()">
                    ⏰ الفواتير المستحقة
                </button>
                <input type="text" class="search-box" placeholder="🔍 البحث في الفواتير..." onkeyup="searchInvoices(this.value)">
            </div>
        </div>

        <!-- نموذج إنشاء فاتورة جديدة -->
        <div id="invoiceForm" class="invoice-form">
            <h2>📝 إنشاء فاتورة مبيعات جديدة</h2>
            <form id="newInvoiceForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="invoiceNumber">رقم الفاتورة:</label>
                        <input type="text" id="invoiceNumber" readonly>
                    </div>
                    <div class="form-group">
                        <label for="invoiceDate">التاريخ:</label>
                        <input type="date" id="invoiceDate" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="customerSelect">العميل:</label>
                        <select id="customerSelect" required>
                            <option value="">اختر العميل</option>
                            <option value="C0001">شركة الأهرام للتجارة</option>
                            <option value="C0002">مؤسسة النيل للخدمات</option>
                            <option value="C0003">شركة الدلتا للصناعات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="paymentMethod">طريقة الدفع:</label>
                        <select id="paymentMethod">
                            <option value="نقدي">نقدي</option>
                            <option value="شيك">شيك</option>
                            <option value="تحويل بنكي">تحويل بنكي</option>
                            <option value="آجل">آجل</option>
                        </select>
                    </div>
                </div>

                <div class="form-row-3">
                    <div class="form-group">
                        <label for="subtotal">الإجمالي قبل الضريبة:</label>
                        <input type="number" id="subtotal" step="0.01" min="0" onchange="calculateTotal()">
                    </div>
                    <div class="form-group">
                        <label for="tax">الضريبة (14%):</label>
                        <input type="number" id="tax" step="0.01" readonly>
                    </div>
                    <div class="form-group">
                        <label for="total">الإجمالي النهائي:</label>
                        <input type="number" id="total" step="0.01" readonly>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="paidAmount">المبلغ المدفوع:</label>
                        <input type="number" id="paidAmount" step="0.01" min="0" onchange="calculateRemaining()">
                    </div>
                    <div class="form-group">
                        <label for="remainingAmount">المبلغ المتبقي:</label>
                        <input type="number" id="remainingAmount" step="0.01" readonly>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="submit" class="btn btn-success">💾 حفظ الفاتورة</button>
                    <button type="button" class="btn btn-danger" onclick="hideInvoiceForm()">❌ إلغاء</button>
                </div>
            </form>
        </div>

        <div class="invoices-table">
            <div class="table-header">
                📋 قائمة فواتير المبيعات
            </div>
            <table id="invoicesTable">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>العميل</th>
                        <th>الإجمالي</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="invoicesTableBody">
                    <!-- سيتم ملء البيانات بـ JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // بيانات الفواتير
        let invoices = [
            {
                number: 'INV20240115001',
                date: '2024-01-15',
                customerCode: 'C0001',
                customerName: 'شركة الأهرام للتجارة',
                subtotal: 10000,
                tax: 1400,
                total: 11400,
                paid: 11400,
                remaining: 0,
                paymentMethod: 'نقدي',
                status: 'مدفوعة'
            },
            {
                number: 'INV20240116001',
                date: '2024-01-16',
                customerCode: 'C0002',
                customerName: 'مؤسسة النيل للخدمات',
                subtotal: 5000,
                tax: 700,
                total: 5700,
                paid: 3000,
                remaining: 2700,
                paymentMethod: 'آجل',
                status: 'جزئية'
            },
            {
                number: 'INV20240117001',
                date: '2024-01-17',
                customerCode: 'C0003',
                customerName: 'شركة الدلتا للصناعات',
                subtotal: 15000,
                tax: 2100,
                total: 17100,
                paid: 17100,
                remaining: 0,
                paymentMethod: 'تحويل بنكي',
                status: 'مدفوعة'
            }
        ];

        // تحميل البيانات عند فتح الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadInvoices();
            updateStats();
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
        });

        function loadInvoices() {
            const tbody = document.getElementById('invoicesTableBody');
            tbody.innerHTML = '';

            invoices.forEach((invoice, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${invoice.number}</td>
                    <td>${invoice.date}</td>
                    <td>${invoice.customerName}</td>
                    <td>${invoice.total.toLocaleString()} ج.م</td>
                    <td>${invoice.paid.toLocaleString()} ج.م</td>
                    <td style="color: ${invoice.remaining > 0 ? '#e74c3c' : '#27ae60'}">
                        ${invoice.remaining.toLocaleString()} ج.م
                    </td>
                    <td>${invoice.paymentMethod}</td>
                    <td>
                        <span class="status-${invoice.status === 'مدفوعة' ? 'paid' : invoice.status === 'جزئية' ? 'partial' : 'pending'}">
                            ${invoice.status}
                        </span>
                    </td>
                    <td>
                        <div class="action-btns">
                            <button class="action-btn btn-primary" onclick="printInvoice(${index})">🖨️ طباعة</button>
                            <button class="action-btn btn-warning" onclick="editInvoice(${index})">✏️ تعديل</button>
                            <button class="action-btn btn-danger" onclick="deleteInvoice(${index})">🗑️ حذف</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateStats() {
            const totalInvoices = invoices.length;
            const totalSales = invoices.reduce((sum, inv) => sum + inv.total, 0);
            const paidAmount = invoices.reduce((sum, inv) => sum + inv.paid, 0);
            const pendingAmount = invoices.reduce((sum, inv) => sum + inv.remaining, 0);

            document.getElementById('totalInvoices').textContent = totalInvoices;
            document.getElementById('totalSales').textContent = totalSales.toLocaleString();
            document.getElementById('paidAmount').textContent = paidAmount.toLocaleString();
            document.getElementById('pendingAmount').textContent = pendingAmount.toLocaleString();
        }

        function showInvoiceForm() {
            document.getElementById('invoiceForm').style.display = 'block';
            document.getElementById('invoiceNumber').value = generateInvoiceNumber();
        }

        function hideInvoiceForm() {
            document.getElementById('invoiceForm').style.display = 'none';
            document.getElementById('newInvoiceForm').reset();
        }

        function generateInvoiceNumber() {
            const today = new Date();
            const dateStr = today.getFullYear().toString() + 
                           (today.getMonth() + 1).toString().padStart(2, '0') + 
                           today.getDate().toString().padStart(2, '0');
            const count = invoices.filter(inv => inv.number.includes(dateStr)).length + 1;
            return 'INV' + dateStr + count.toString().padStart(3, '0');
        }

        function calculateTotal() {
            const subtotal = parseFloat(document.getElementById('subtotal').value) || 0;
            const tax = subtotal * 0.14;
            const total = subtotal + tax;
            
            document.getElementById('tax').value = tax.toFixed(2);
            document.getElementById('total').value = total.toFixed(2);
            document.getElementById('paidAmount').value = total.toFixed(2);
            calculateRemaining();
        }

        function calculateRemaining() {
            const total = parseFloat(document.getElementById('total').value) || 0;
            const paid = parseFloat(document.getElementById('paidAmount').value) || 0;
            const remaining = total - paid;
            
            document.getElementById('remainingAmount').value = remaining.toFixed(2);
        }

        // حفظ الفاتورة الجديدة
        document.getElementById('newInvoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const customerSelect = document.getElementById('customerSelect');
            const customerName = customerSelect.options[customerSelect.selectedIndex].text;
            
            const invoiceData = {
                number: document.getElementById('invoiceNumber').value,
                date: document.getElementById('invoiceDate').value,
                customerCode: document.getElementById('customerSelect').value,
                customerName: customerName === 'اختر العميل' ? '' : customerName,
                subtotal: parseFloat(document.getElementById('subtotal').value) || 0,
                tax: parseFloat(document.getElementById('tax').value) || 0,
                total: parseFloat(document.getElementById('total').value) || 0,
                paid: parseFloat(document.getElementById('paidAmount').value) || 0,
                remaining: parseFloat(document.getElementById('remainingAmount').value) || 0,
                paymentMethod: document.getElementById('paymentMethod').value,
                status: parseFloat(document.getElementById('remainingAmount').value) === 0 ? 'مدفوعة' : 'جزئية'
            };

            invoices.push(invoiceData);
            loadInvoices();
            updateStats();
            hideInvoiceForm();
            alert('تم إنشاء الفاتورة بنجاح!');
        });

        function searchInvoices(searchTerm) {
            const tbody = document.getElementById('invoicesTableBody');
            const rows = tbody.getElementsByTagName('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm.toLowerCase())) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        }

        function printInvoice(index) {
            const invoice = invoices[index];
            alert(`طباعة الفاتورة: ${invoice.number}\n\nالعميل: ${invoice.customerName}\nالإجمالي: ${invoice.total.toLocaleString()} ج.م\n\nسيتم فتح نافذة الطباعة...`);
        }

        function editInvoice(index) {
            alert('ميزة تعديل الفاتورة ستكون متاحة قريباً...');
        }

        function deleteInvoice(index) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                invoices.splice(index, 1);
                loadInvoices();
                updateStats();
                alert('تم حذف الفاتورة بنجاح');
            }
        }

        function exportInvoices() {
            const csvContent = "data:text/csv;charset=utf-8," 
                + "رقم الفاتورة,التاريخ,كود العميل,اسم العميل,الإجمالي قبل الضريبة,الضريبة,الإجمالي النهائي,المدفوع,المتبقي,طريقة الدفع,الحالة\n"
                + invoices.map(inv => `${inv.number},${inv.date},${inv.customerCode},${inv.customerName},${inv.subtotal},${inv.tax},${inv.total},${inv.paid},${inv.remaining},${inv.paymentMethod},${inv.status}`).join("\n");
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "فواتير_المبيعات_نيو_جرافيك.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function showPendingInvoices() {
            const pendingInvoices = invoices.filter(inv => inv.remaining > 0);
            
            if (pendingInvoices.length === 0) {
                alert('🎉 ممتاز! جميع الفواتير مدفوعة بالكامل');
                return;
            }
            
            let message = '⏰ الفواتير المستحقة:\n\n';
            pendingInvoices.forEach(inv => {
                message += `• ${inv.number} - ${inv.customerName}\n  المبلغ المستحق: ${inv.remaining.toLocaleString()} ج.م\n\n`;
            });
            
            alert(message);
        }
    </script>
</body>
</html>
